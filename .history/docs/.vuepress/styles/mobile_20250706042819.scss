// 移动端专用样式
// 针对双马智能科技移动端设计优化

@use 'sass:map';

// 导入响应式断点和mixins
$breakpoints: (
  'xs': 0,
  'sm': 576px,
  'md': 768px,
  'lg': 992px,
  'xl': 1200px,
  'xxl': 1400px
);

// 响应式混入
@mixin respond-to($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    $value: map-get($breakpoints, $breakpoint);
    @if $value == 0 {
      @content;
    } @else {
      @media (min-width: $value) {
        @content;
      }
    }
  }
}

// ===== 移动端导航样式 =====
.mobile-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: var(--z-fixed);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--brand-light-gray);
  
  .nav-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-3) var(--spacing-4);
    height: 60px;
  }
  
  .nav-logo {
    height: 40px;
    width: auto;
  }
  
  .nav-back {
    display: flex;
    align-items: center;
    color: var(--brand-dark-gray);
    text-decoration: none;
    font-size: var(--font-size-base);
    
    &::before {
      content: '←';
      margin-right: var(--spacing-2);
      font-size: var(--font-size-lg);
    }
  }
  
  .nav-title {
    font-size: var(--font-size-lg);
    font-weight: 500;
    color: var(--brand-dark-gray);
  }
  
  .nav-home {
    display: flex;
    align-items: center;
    color: var(--brand-dark-gray);
    text-decoration: none;
    
    &::after {
      content: '🏠';
      font-size: var(--font-size-lg);
    }
  }
}

// ===== 移动端页面布局 =====
.mobile-page {
  min-height: 100vh;
  padding-top: 60px; // 为固定导航留出空间
  
  .page-content {
    padding: var(--spacing-4);
  }
}

// ===== 移动端卡片网格 =====
.mobile-grid {
  display: grid;
  gap: var(--spacing-4);
  
  &.grid-2 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  &.grid-1 {
    grid-template-columns: 1fr;
  }
  
  @include respond-to('md') {
    gap: var(--spacing-6);
    
    &.grid-2 {
      grid-template-columns: repeat(2, 1fr);
    }
    
    &.grid-3 {
      grid-template-columns: repeat(3, 1fr);
    }
  }
}

// ===== 移动端导航卡片 =====
.nav-card {
  // 基础卡片样式
  background: var(--brand-white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  transition: all var(--transition-normal);

  // 导航卡片特有样式
  padding: var(--spacing-6);
  text-align: center;
  text-decoration: none;
  color: inherit;
  position: relative;

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
  }
  
  .card-icon {
    width: 48px;
    height: 48px;
    margin: 0 auto var(--spacing-4);
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--gradient-primary);
    border-radius: var(--radius-lg);
    color: var(--brand-white);
    font-size: var(--font-size-2xl);
  }
  
  .card-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--brand-dark-gray);
    margin-bottom: var(--spacing-2);
  }
  
  .card-subtitle {
    font-size: var(--font-size-sm);
    color: #666;
    margin: 0;
  }
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left var(--transition-slow);
  }
  
  &:hover::before {
    left: 100%;
  }
}

// ===== 移动端英雄区域 =====
.mobile-hero {
  background: var(--brand-secondary);
  color: var(--brand-white);
  text-align: center;
  padding: var(--spacing-12) var(--spacing-4);
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.1;
  }
  
  .hero-content {
    position: relative;
    z-index: 1;
  }
  
  .hero-logo {
    width: 120px;
    height: auto;
    margin-bottom: var(--spacing-6);
  }
  
  .hero-title {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    margin-bottom: var(--spacing-4);
    
    @include respond-to('md') {
      font-size: var(--font-size-4xl);
    }
  }
  
  .hero-subtitle {
    font-size: var(--font-size-lg);
    opacity: 0.9;
    margin-bottom: var(--spacing-8);
  }
  
  .hero-image {
    max-width: 100%;
    height: auto;
    margin: var(--spacing-8) 0;
  }
}

// ===== 移动端内容区域 =====
.mobile-content {
  .content-header {
    background: var(--gradient-primary);
    color: var(--brand-white);
    padding: var(--spacing-4);
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
    text-align: center;
    font-weight: 600;
    font-size: var(--font-size-lg);
  }
  
  .content-body {
    padding: var(--spacing-6);
    line-height: 1.8;
    
    h1, h2, h3, h4, h5, h6 {
      color: var(--brand-dark-gray);
      margin-top: var(--spacing-6);
      margin-bottom: var(--spacing-4);
    }
    
    p {
      margin-bottom: var(--spacing-4);
      color: #555;
    }
    
    img {
      width: 100%;
      height: auto;
      border-radius: var(--radius-md);
      margin: var(--spacing-4) 0;
    }
  }
}

// ===== 移动端页脚 =====
.mobile-footer {
  background: var(--brand-dark-gray);
  color: var(--brand-white);
  padding: var(--spacing-8) var(--spacing-4);
  text-align: center;
  
  .footer-content {
    max-width: 600px;
    margin: 0 auto;
  }
  
  .footer-links {
    display: flex;
    justify-content: center;
    gap: var(--spacing-6);
    margin-bottom: var(--spacing-6);
    
    a {
      color: var(--brand-white);
      text-decoration: none;
      font-size: var(--font-size-sm);
      opacity: 0.8;
      transition: opacity var(--transition-normal);
      
      &:hover {
        opacity: 1;
      }
    }
  }
  
  .footer-copyright {
    font-size: var(--font-size-xs);
    opacity: 0.6;
    margin: 0;
  }
}

// ===== 移动端动画 =====
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

// ===== 移动端滚动指示器 =====
.scroll-indicator {
  position: fixed;
  bottom: var(--spacing-6);
  left: 50%;
  transform: translateX(-50%);
  background: var(--brand-primary);
  color: var(--brand-white);
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  box-shadow: var(--shadow-lg);
  z-index: var(--z-fixed);
  
  &::before {
    content: '↓';
    margin-right: var(--spacing-1);
    animation: bounce 2s infinite;
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-5px);
  }
  60% {
    transform: translateY(-3px);
  }
}
