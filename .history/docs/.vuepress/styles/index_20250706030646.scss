// 双马智能科技 - 全局样式
// 导入图标样式
@import './icons.scss';

// 品牌色彩变量
:root {
  // 主色调
  --brand-red: #e53e3e;
  --brand-dark: #1a1a1a;
  --brand-black: #000000;
  --brand-white: #ffffff;
  --brand-gray: #666666;
  --brand-light-gray: #f5f5f5;
  
  // 渐变色
  --gradient-red: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
  --gradient-dark: linear-gradient(135deg, #1a1a1a 0%, #000000 100%);
  
  // 阴影
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.2);
  --shadow-heavy: 0 8px 32px rgba(0, 0, 0, 0.3);
  
  // 边框圆角
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
  --border-radius-xl: 16px;
  
  // 间距
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-2xl: 48px;
  --spacing-3xl: 64px;
  
  // 字体大小
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-md: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-2xl: 24px;
  --font-size-3xl: 32px;
  --font-size-4xl: 40px;
  
  // 响应式断点
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
}

// 全局重置样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: var(--brand-white);
  background: var(--brand-black);
  overflow-x: hidden;
}

// 覆盖VuePress默认样式
.theme-default-content {
  background: var(--brand-black) !important;
  color: var(--brand-white) !important;
}

.navbar {
  background: var(--brand-black) !important;
  border-bottom: 1px solid rgba(229, 62, 62, 0.2) !important;
}

.navbar-brand {
  color: var(--brand-white) !important;
}

.navbar-items a {
  color: var(--brand-white) !important;
  transition: color 0.3s ease;
  
  &:hover {
    color: var(--brand-red) !important;
  }
}

// 自定义滚动条
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--brand-dark);
}

::-webkit-scrollbar-thumb {
  background: var(--brand-red);
  border-radius: 4px;
  
  &:hover {
    background: #c53030;
  }
}

// 通用工具类
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

// 按钮样式
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-lg);
  border: none;
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-md);
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &.btn-primary {
    background: var(--gradient-red);
    color: var(--brand-white);
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-medium);
    }
  }
  
  &.btn-secondary {
    background: transparent;
    color: var(--brand-white);
    border: 2px solid var(--brand-red);
    
    &:hover {
      background: var(--brand-red);
      color: var(--brand-white);
    }
  }
}

// 卡片样式
.card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-heavy);
    border-color: var(--brand-red);
  }
}

// 响应式工具类
@media (max-width: 768px) {
  .container {
    padding: 0 var(--spacing-sm);
  }
  
  .hidden-mobile {
    display: none !important;
  }
}

@media (min-width: 769px) {
  .hidden-desktop {
    display: none !important;
  }
}

// 动画
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.fade-in {
  animation: fadeIn 0.6s ease-out;
}

.slide-in-left {
  animation: slideInLeft 0.6s ease-out;
}

.slide-in-right {
  animation: slideInRight 0.6s ease-out;
}
