// 移动端优先的响应式设计系统
// 基于双马智能科技设计稿创建

@use 'sass:map';
@use './mobile.scss';

// ===== CSS变量系统 =====
:root {
  // 品牌色彩
  --brand-primary: #e53e3e; // 双马红色
  --brand-secondary: #000000; // 黑色
  --brand-white: #ffffff;
  --brand-gray: #f5f5f5;
  --brand-dark-gray: #333333;
  --brand-light-gray: #cccccc;
  
  // 渐变色
  --gradient-primary: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
  --gradient-dark: linear-gradient(135deg, #000000 0%, #2d3748 100%);
  
  // 字体大小 (移动端优先)
  --font-size-xs: 0.75rem;   // 12px
  --font-size-sm: 0.875rem;  // 14px
  --font-size-base: 1rem;    // 16px
  --font-size-lg: 1.125rem;  // 18px
  --font-size-xl: 1.25rem;   // 20px
  --font-size-2xl: 1.5rem;   // 24px
  --font-size-3xl: 1.875rem; // 30px
  --font-size-4xl: 2.25rem;  // 36px
  
  // 间距系统
  --spacing-1: 0.25rem;  // 4px
  --spacing-2: 0.5rem;   // 8px
  --spacing-3: 0.75rem;  // 12px
  --spacing-4: 1rem;     // 16px
  --spacing-5: 1.25rem;  // 20px
  --spacing-6: 1.5rem;   // 24px
  --spacing-8: 2rem;     // 32px
  --spacing-10: 2.5rem;  // 40px
  --spacing-12: 3rem;    // 48px
  --spacing-16: 4rem;    // 64px
  
  // 圆角
  --radius-sm: 0.25rem;  // 4px
  --radius-md: 0.5rem;   // 8px
  --radius-lg: 0.75rem;  // 12px
  --radius-xl: 1rem;     // 16px
  --radius-full: 9999px;
  
  // 阴影
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  // 过渡动画
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
  
  // Z-index层级
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
}

// ===== 响应式断点 =====
$breakpoints: (
  'xs': 0,
  'sm': 576px,
  'md': 768px,
  'lg': 992px,
  'xl': 1200px,
  'xxl': 1400px
);

// 响应式混入
@mixin respond-to($breakpoint) {
  @if map.has-key($breakpoints, $breakpoint) {
    $value: map.get($breakpoints, $breakpoint);
    @if $value == 0 {
      @content;
    } @else {
      @media (min-width: $value) {
        @content;
      }
    }
  }
}

// ===== 基础重置样式 =====
* {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: var(--font-size-base);
  line-height: 1.6;
  color: var(--brand-dark-gray);
  background-color: var(--brand-white);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// ===== 移动端优化基础类 =====
.mobile-container {
  width: 100%;
  max-width: 100vw;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
  
  @include respond-to('sm') {
    padding: 0 var(--spacing-6);
  }
  
  @include respond-to('lg') {
    max-width: 1200px;
    padding: 0 var(--spacing-8);
  }
}

.mobile-section {
  padding: var(--spacing-8) 0;
  
  @include respond-to('md') {
    padding: var(--spacing-12) 0;
  }
}

// ===== 布局工具类 =====
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.text-center {
  text-align: center;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

// ===== 按钮基础样式 =====
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-3) var(--spacing-6);
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-normal);
  user-select: none;
  
  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.2);
  }
  
  &.btn-primary {
    background: var(--gradient-primary);
    color: var(--brand-white);
    
    &:hover {
      transform: translateY(-1px);
      box-shadow: var(--shadow-lg);
    }
  }
  
  &.btn-secondary {
    background: var(--brand-white);
    color: var(--brand-dark-gray);
    border: 1px solid var(--brand-light-gray);
    
    &:hover {
      background: var(--brand-gray);
    }
  }
}

// ===== 卡片基础样式 =====
.card {
  background: var(--brand-white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  transition: all var(--transition-normal);
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
  }
}

// ===== 文字样式 =====
.text-primary {
  color: var(--brand-primary);
}

.text-secondary {
  color: var(--brand-secondary);
}

.text-gray {
  color: var(--brand-dark-gray);
}

// ===== 移动端触摸优化 =====
.touch-target {
  min-height: 44px;
  min-width: 44px;
}

// ===== 隐藏滚动条但保持功能 =====
.hide-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;

  &::-webkit-scrollbar {
    display: none;
  }
}


