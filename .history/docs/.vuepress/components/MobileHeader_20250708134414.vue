<template>
  <header class="mobile-nav">
    <div v-if="!hideNavContent" class="nav-content">
      <!-- 返回按钮 -->
      <RouteLink
        v-if="showBack"
        :to="backUrl"
        class="nav-back"
        @click="handleBack"
      >
        返回
      </RouteLink>

      <!-- Logo -->
      <div v-else-if="showLogo" class="nav-logo-container">
        <img
          :src="logoUrl"
          :alt="logoAlt"
          class="nav-logo"
        />
      </div>

      <!-- 占位符 -->
      <div v-else class="nav-placeholder"></div>

      <!-- 页面标题 -->
      <h1 v-if="title" class="nav-title">{{ title }}</h1>
      <div v-else class="nav-title-placeholder"></div>

      <!-- 首页按钮 -->
      <RouteLink
        v-if="showHome"
        to="/"
        class="nav-home"
        aria-label="返回首页"
      >
        <span></span>
      </RouteLink>

      <!-- 占位符 -->
      <div v-else class="nav-placeholder"></div>
    </div>
  </header>
</template>

<script setup>
import { RouteLink } from 'vuepress/client'
// Props定义
const props = defineProps({
  // 页面标题
  title: {
    type: String,
    default: ''
  },
  // 是否显示返回按钮
  showBack: {
    type: Boolean,
    default: false
  },
  // 返回URL
  backUrl: {
    type: String,
    default: '/'
  },
  // 是否显示Logo
  showLogo: {
    type: Boolean,
    default: true
  },
  // Logo URL
  logoUrl: {
    type: String,
    default: 'https://img-public.hui1688.cn/shuangma/logo.jpg'
  },
  // Logo Alt文本
  logoAlt: {
    type: String,
    default: '双马智能科技'
  },
  // 是否显示首页按钮
  showHome: {
    type: Boolean,
    default: true
  },
  // 自定义返回处理
  customBack: {
    type: Function,
    default: null
  },
  // 是否隐藏导航内容（用于首页）
  hideNavContent: {
    type: Boolean,
    default: false
  }
})

// 处理返回操作
const handleBack = (event) => {
  if (props.customBack) {
    event.preventDefault()
    props.customBack()
  }
}
</script>

<style lang="scss" scoped>
.mobile-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: var(--z-fixed);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--brand-light-gray);
  
  .nav-content {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr;
    align-items: center;
    padding: var(--spacing-3) var(--spacing-4);
    height: 60px;
    max-width: 100%;
  }
  
  .nav-back {
    display: flex;
    align-items: center;
    color: var(--brand-dark-gray);
    text-decoration: none;
    font-size: var(--font-size-base);
    font-weight: 500;
    transition: color var(--transition-normal);
    
    &::before {
      content: '←';
      margin-right: var(--spacing-2);
      font-size: var(--font-size-lg);
    }
    
    &:hover {
      color: var(--brand-primary);
    }
  }
  
  .nav-logo-container {
    display: flex;
    justify-content: flex-start;
  }
  
  .nav-logo {
    height: 40px;
    width: auto;
    max-width: 120px;
  }
  
  .nav-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--brand-dark-gray);
    margin: 0;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .nav-home {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    color: var(--brand-dark-gray);
    text-decoration: none;
    font-size: var(--font-size-lg);
    transition: color var(--transition-normal);
    
    &::after {
      content: '🏠';
      font-size: var(--font-size-xl);
    }
    
    &:hover {
      color: var(--brand-primary);
    }
  }
  
  .nav-placeholder,
  .nav-title-placeholder {
    width: 100%;
  }
}

// 响应式调整
@media (max-width: 480px) {
  .mobile-nav {
    .nav-content {
      padding: var(--spacing-2) var(--spacing-3);
    }
    
    .nav-title {
      font-size: var(--font-size-base);
    }
    
    .nav-back {
      font-size: var(--font-size-sm);
    }
  }
}
</style>
