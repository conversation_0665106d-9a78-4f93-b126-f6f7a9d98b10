<template>
  <component 
    :is="linkComponent"
    :to="to"
    :href="href"
    :target="target"
    class="nav-card"
    :class="cardClasses"
    @click="handleClick"
  >
    <!-- 图标区域 -->
    <div class="card-icon" :style="iconStyle">
      <component v-if="iconComponent" :is="iconComponent" />
      <span v-else-if="iconText">{{ iconText }}</span>
      <img v-else-if="iconImage" :src="iconImage" :alt="title" />
    </div>
    
    <!-- 内容区域 -->
    <div class="card-content">
      <h3 class="card-title">{{ title }}</h3>
      <p v-if="subtitle" class="card-subtitle">{{ subtitle }}</p>
      <p v-if="description" class="card-description">{{ description }}</p>
    </div>
    
    <!-- 角标 -->
    <div v-if="badge" class="card-badge">{{ badge }}</div>
    
    <!-- 箭头指示器 -->
    <div v-if="showArrow" class="card-arrow">→</div>
  </component>
</template>

<script setup>
import { computed } from 'vue'

// Props定义
const props = defineProps({
  // 卡片标题
  title: {
    type: String,
    required: true
  },
  // 副标题
  subtitle: {
    type: String,
    default: ''
  },
  // 描述文本
  description: {
    type: String,
    default: ''
  },
  // 路由链接 (内部链接)
  to: {
    type: [String, Object],
    default: null
  },
  // 外部链接
  href: {
    type: String,
    default: null
  },
  // 链接目标
  target: {
    type: String,
    default: '_self'
  },
  // 图标组件
  iconComponent: {
    type: [String, Object],
    default: null
  },
  // 图标文本 (emoji或文字)
  iconText: {
    type: String,
    default: ''
  },
  // 图标图片
  iconImage: {
    type: String,
    default: ''
  },
  // 图标背景色
  iconBgColor: {
    type: String,
    default: ''
  },
  // 卡片尺寸
  size: {
    type: String,
    default: 'normal', // normal, large, small
    validator: (value) => ['small', 'normal', 'large'].includes(value)
  },
  // 卡片样式
  variant: {
    type: String,
    default: 'default', // default, primary, secondary
    validator: (value) => ['default', 'primary', 'secondary'].includes(value)
  },
  // 角标文本
  badge: {
    type: String,
    default: ''
  },
  // 是否显示箭头
  showArrow: {
    type: Boolean,
    default: true
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false
  },
  // 点击事件处理
  onClick: {
    type: Function,
    default: null
  }
})

// 计算链接组件类型
const linkComponent = computed(() => {
  if (props.disabled) return 'div'
  if (props.to) return 'RouteLink'
  if (props.href) return 'a'
  return 'div'
})

// 计算卡片样式类
const cardClasses = computed(() => {
  return [
    `nav-card--${props.size}`,
    `nav-card--${props.variant}`,
    {
      'nav-card--disabled': props.disabled,
      'nav-card--clickable': !props.disabled && (props.to || props.href || props.onClick)
    }
  ]
})

// 计算图标样式
const iconStyle = computed(() => {
  const style = {}
  if (props.iconBgColor) {
    style.background = props.iconBgColor
  }
  return style
})

// 处理点击事件
const handleClick = (event) => {
  if (props.disabled) {
    event.preventDefault()
    return
  }
  
  if (props.onClick) {
    props.onClick(event)
  }
}
</script>

<style lang="scss" scoped>
.nav-card {
  background: var(--brand-white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  transition: all var(--transition-normal);
  text-decoration: none;
  color: inherit;
  position: relative;
  display: block;
  
  // 基础悬停效果
  &:hover:not(.nav-card--disabled) {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
  }
  
  // 尺寸变体
  &.nav-card--small {
    .card-content {
      padding: var(--spacing-4);
    }
    
    .card-icon {
      width: 36px;
      height: 36px;
      font-size: var(--font-size-lg);
    }
    
    .card-title {
      font-size: var(--font-size-base);
    }
  }
  
  &.nav-card--normal {
    .card-content {
      padding: var(--spacing-6);
    }
    
    .card-icon {
      width: 48px;
      height: 48px;
      font-size: var(--font-size-xl);
    }
  }
  
  &.nav-card--large {
    .card-content {
      padding: var(--spacing-8);
    }
    
    .card-icon {
      width: 64px;
      height: 64px;
      font-size: var(--font-size-2xl);
    }
    
    .card-title {
      font-size: var(--font-size-xl);
    }
  }
  
  // 样式变体
  &.nav-card--primary {
    background: var(--gradient-primary);
    color: var(--brand-white);
    
    .card-icon {
      background: rgba(255, 255, 255, 0.2);
      color: var(--brand-white);
    }
  }
  
  &.nav-card--secondary {
    background: var(--brand-gray);
    
    .card-icon {
      background: var(--brand-white);
    }
  }
  
  // 禁用状态
  &.nav-card--disabled {
    opacity: 0.5;
    cursor: not-allowed;
    
    &:hover {
      transform: none;
      box-shadow: var(--shadow-md);
    }
  }
  
  // 可点击状态
  &.nav-card--clickable {
    cursor: pointer;
  }
}

.card-content {
  text-align: center;
  position: relative;
}

.card-icon {
  width: 48px;
  height: 48px;
  margin: 0 auto var(--spacing-4);
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--gradient-primary);
  border-radius: var(--radius-lg);
  color: var(--brand-white);
  font-size: var(--font-size-xl);
  
  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}

.card-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--brand-dark-gray);
  margin: 0 0 var(--spacing-2);
  line-height: 1.4;
}

.card-subtitle {
  font-size: var(--font-size-sm);
  color: #666;
  margin: 0 0 var(--spacing-2);
  font-weight: 500;
}

.card-description {
  font-size: var(--font-size-sm);
  color: #888;
  margin: 0;
  line-height: 1.5;
}

.card-badge {
  position: absolute;
  top: var(--spacing-2);
  right: var(--spacing-2);
  background: var(--brand-primary);
  color: var(--brand-white);
  font-size: var(--font-size-xs);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-full);
  font-weight: 500;
}

.card-arrow {
  position: absolute;
  bottom: var(--spacing-3);
  right: var(--spacing-3);
  color: var(--brand-primary);
  font-size: var(--font-size-lg);
  opacity: 0.7;
  transition: all var(--transition-normal);
}

// 光泽效果
.nav-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left var(--transition-slow);
  z-index: 1;
}

.nav-card:hover::before {
  left: 100%;
}

// 确保内容在光泽效果之上
.card-content,
.card-badge,
.card-arrow {
  position: relative;
  z-index: 2;
}
</style>
