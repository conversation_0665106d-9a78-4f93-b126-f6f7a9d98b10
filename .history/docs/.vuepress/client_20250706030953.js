import { defineClientConfig } from 'vuepress/client'
import CompanyProfile from './components/CompanyProfile.vue'
import HomePage from './components/HomePage.vue'
import Article from './layouts/Article.vue'
import Category from './layouts/Category.vue'
import Layout from './layouts/Layout.vue'
import Tag from './layouts/Tag.vue'
import Timeline from './layouts/Timeline.vue'

// 导入全局样式
import './styles/index.scss'

export default defineClientConfig({
  enhance({ app, router, siteData }) {
    // 注册全局组件
    app.component('HomePage', HomePage)
    app.component('CompanyProfile', CompanyProfile)
  },
  // we provide some blog layouts
  layouts: {
    Article,
    Category,
    Tag,
    Timeline,
    Layout,
  },
})
