import { viteBundler } from '@vuepress/bundler-vite'
import { blogPlugin } from '@vuepress/plugin-blog'
import { defaultTheme } from '@vuepress/theme-default'
import { defineUserConfig } from 'vuepress'

export default defineUserConfig({
  lang: 'zh-CN',

  title: '双马智能科技',
  description: '电动车仪表专业生产制造商',

  head: [
    ['link', { rel: 'icon', href: '/images/favicon.ico' }],
    ['meta', { name: 'viewport', content: 'width=device-width, initial-scale=1.0' }],
    ['meta', { name: 'keywords', content: '双马智能科技,电动车仪表,电动摩托车,LCD系列,国标系列' }],
  ],

  theme: defaultTheme({
    logo: '/images/logo.png',
    logoDark: '/images/logo.png',

    navbar: [
      {
        text: '首页',
        link: '/',
      },
      {
        text: '公司简介',
        link: '/company/',
      },
      {
        text: '产品系列',
        children: [
          {
            text: '本月新品',
            link: '/new-products/',
          },
          {
            text: '新品发布',
            link: '/new-release/',
          },
          {
            text: '电摩系列',
            link: '/electric-series/',
          },
          {
            text: '国标系列',
            link: '/national-series/',
          },
          {
            text: 'LCD系列',
            link: '/lcd-series/',
          },
        ],
      },
      {
        text: '生产车间',
        link: '/workshop/',
      },
      {
        text: '资质证书',
        link: '/qualification/',
      },
    ],

    sidebar: {
      '/company/': [
        {
          text: '公司信息',
          children: [
            '/company/README.md',
            '/company/history.md',
            '/company/culture.md',
          ],
        },
      ],
      '/products/': [
        {
          text: '产品系列',
          children: [
            '/products/README.md',
            '/products/electric-series.md',
            '/products/national-series.md',
            '/products/lcd-series.md',
          ],
        },
      ],
    },

    // 自定义样式
    colorMode: 'dark',
    colorModeSwitch: false,
  }),

  plugins: [
    blogPlugin({
      // Only files under posts are articles
      filter: ({ filePathRelative }) =>
        filePathRelative ? filePathRelative.startsWith('posts/') : false,

      // Getting article info
      getInfo: ({ frontmatter, title, data }) => ({
        title,
        author: frontmatter.author || '',
        date: frontmatter.date || null,
        category: frontmatter.category || [],
        tag: frontmatter.tag || [],
        excerpt:
          // Support manually set excerpt through frontmatter
          typeof frontmatter.excerpt === 'string'
            ? frontmatter.excerpt
            : data?.excerpt || '',
      }),

      // Generate excerpt for all pages excerpt those users choose to disable
      excerptFilter: ({ frontmatter }) =>
        !frontmatter.home &&
        frontmatter.excerpt !== false &&
        typeof frontmatter.excerpt !== 'string',

      category: [
        {
          key: 'category',
          getter: (page) => page.frontmatter.category || [],
          layout: 'Category',
          itemLayout: 'Category',
          frontmatter: () => ({
            title: 'Categories',
            sidebar: false,
          }),
          itemFrontmatter: (name) => ({
            title: `Category ${name}`,
            sidebar: false,
          }),
        },
        {
          key: 'tag',
          getter: (page) => page.frontmatter.tag || [],
          layout: 'Tag',
          itemLayout: 'Tag',
          frontmatter: () => ({
            title: 'Tags',
            sidebar: false,
          }),
          itemFrontmatter: (name) => ({
            title: `Tag ${name}`,
            sidebar: false,
          }),
        },
      ],

      type: [
        {
          key: 'article',
          // Remove archive articles
          filter: (page) => !page.frontmatter.archive,
          layout: 'Article',
          frontmatter: () => ({
            title: 'Articles',
            sidebar: false,
          }),
          // Sort pages with time and sticky
          sorter: (pageA, pageB) => {
            if (pageA.frontmatter.sticky && pageB.frontmatter.sticky)
              return pageB.frontmatter.sticky - pageA.frontmatter.sticky

            if (pageA.frontmatter.sticky && !pageB.frontmatter.sticky) return -1

            if (!pageA.frontmatter.sticky && pageB.frontmatter.sticky) return 1

            if (!pageB.frontmatter.date) return 1
            if (!pageA.frontmatter.date) return -1

            return (
              new Date(pageB.frontmatter.date).getTime() -
              new Date(pageA.frontmatter.date).getTime()
            )
          },
        },
        {
          key: 'timeline',
          // Only article with date should be added to timeline
          filter: (page) => page.frontmatter.date instanceof Date,
          // Sort pages with time
          sorter: (pageA, pageB) =>
            new Date(pageB.frontmatter.date).getTime() -
            new Date(pageA.frontmatter.date).getTime(),
          layout: 'Timeline',
          frontmatter: () => ({
            title: 'Timeline',
            sidebar: false,
          }),
        },
      ],
      hotReload: true,
    }),
  ],

  bundler: viteBundler(),
})
