<template>
    <div>
        <h1 id="图片资源说明" tabindex="-1"><a class="header-anchor" href="#图片资源说明"><span>图片资源说明</span></a></h1>
        <p>这个目录包含网站所需的图片资源。请将以下图片文件放置在此目录中：</p>
        <h2 id="必需的图片文件" tabindex="-1"><a class="header-anchor" href="#必需的图片文件"><span>必需的图片文件</span></a></h2>
        <h3 id="_1-logo-png" tabindex="-1"><a class="header-anchor" href="#_1-logo-png"><span>1. logo.png</span></a>
        </h3>
        <ul>
            <li>双马智能科技的公司Logo</li>
            <li>建议尺寸：120x120px</li>
            <li>格式：PNG（支持透明背景）</li>
            <li>用途：导航栏品牌标识</li>
        </ul>
        <h3 id="_2-favicon-ico" tabindex="-1"><a class="header-anchor" href="#_2-favicon-ico"><span>2.
                    favicon.ico</span></a></h3>
        <ul>
            <li>网站图标</li>
            <li>尺寸：16x16px, 32x32px, 48x48px</li>
            <li>格式：ICO</li>
            <li>用途：浏览器标签页图标</li>
        </ul>
        <h3 id="_3-motorcycle-png" tabindex="-1"><a class="header-anchor" href="#_3-motorcycle-png"><span>3.
                    motorcycle.png</span></a></h3>
        <ul>
            <li>电动摩托车产品图片</li>
            <li>建议尺寸：600x800px</li>
            <li>格式：PNG（支持透明背景）</li>
            <li>用途：首页主要产品展示</li>
        </ul>
        <h3 id="_4-company-building-jpg" tabindex="-1"><a class="header-anchor" href="#_4-company-building-jpg"><span>4.
                    company-building.jpg</span></a></h3>
        <ul>
            <li>公司大楼外观图片</li>
            <li>建议尺寸：800x400px</li>
            <li>格式：JPG</li>
            <li>用途：公司简介页面</li>
        </ul>
        <h2 id="图片优化建议" tabindex="-1"><a class="header-anchor" href="#图片优化建议"><span>图片优化建议</span></a></h2>
        <ol>
            <li>
                <p><strong>文件大小控制</strong></p>
                <ul>
                    <li>单个图片文件不超过500KB</li>
                    <li>使用适当的压缩比例</li>
                    <li>考虑使用WebP格式提升加载速度</li>
                </ul>
            </li>
            <li>
                <p><strong>响应式图片</strong></p>
                <ul>
                    <li>提供多种尺寸版本</li>
                    <li>使用srcset属性适配不同设备</li>
                </ul>
            </li>
            <li>
                <p><strong>SEO优化</strong></p>
                <ul>
                    <li>为所有图片添加alt属性</li>
                    <li>使用描述性的文件名</li>
                </ul>
            </li>
        </ol>
        <h2 id="临时占位图片" tabindex="-1"><a class="header-anchor" href="#临时占位图片"><span>临时占位图片</span></a></h2>
        <p>在开发阶段，可以使用以下在线占位图片服务：</p>
        <ul>
            <li>https://via.placeholder.com/600x800/000000/FFFFFF?text=Motorcycle</li>
            <li>https://via.placeholder.com/120x120/E53E3E/FFFFFF?text=Logo</li>
            <li>https://via.placeholder.com/800x400/1A1A1A/FFFFFF?text=Company</li>
        </ul>
        <h2 id="图片版权说明" tabindex="-1"><a class="header-anchor" href="#图片版权说明"><span>图片版权说明</span></a></h2>
        <p>请确保所有使用的图片都有合法的使用权限，避免版权纠纷。</p>
    </div>
</template>
