<template>
  <div class="mobile-page home-page">
    <!-- 移动端导航头部 -->
    <MobileHeader 
      :show-back="false"
      :show-logo="false"
      :show-home="false"
    />
    
    <!-- 英雄区域 -->
    <section class="mobile-hero">
      <div class="hero-content">
        <!-- 品牌Logo -->
        <div class="hero-logo-container">
          <img 
            src="https://img-public.hui1688.cn/shuangma/logo.jpg" 
            alt="双马智能科技" 
            class="hero-logo"
          />
        </div>
        
        <!-- 品牌标题 -->
        <h1 class="hero-title">双马智能科技</h1>
        <p class="hero-subtitle">电动车仪表专业生产制造商</p>
        
        <!-- 产品展示图 -->
        <div class="hero-product">
          <img 
            src="https://img-public.hui1688.cn/shuangma/hero-product.jpg" 
            alt="双马智能科技产品" 
            class="hero-image"
          />
        </div>
      </div>
      
      <!-- 装饰性背景线条 -->
      <div class="hero-decoration">
        <div class="decoration-lines">
          <div v-for="i in 12" :key="i" class="decoration-line" :style="getLineStyle(i)"></div>
        </div>
      </div>
    </section>
    
    <!-- 导航卡片区域 -->
    <section class="navigation-section mobile-section">
      <div class="mobile-container">
        <div class="mobile-grid grid-2">
          <!-- 公司简介卡片 -->
          <NavigationCard
            title="公司简介"
            subtitle="Company profile"
            icon-text="🏢"
            to="/company/"
            :show-arrow="true"
          />
          
          <!-- 产品展示卡片 -->
          <NavigationCard
            title="产品展示"
            subtitle="Product display"
            icon-text="🔧"
            to="/products/"
            :show-arrow="true"
          />
          
          <!-- 生产车间卡片 -->
          <NavigationCard
            title="生产车间"
            subtitle="Production workshop"
            icon-text="🏭"
            to="/workshop/"
            :show-arrow="true"
          />
          
          <!-- 资质证书卡片 -->
          <NavigationCard
            title="资质证书"
            subtitle="Qualification certificates"
            icon-text="🎓"
            to="/qualification/"
            :show-arrow="true"
          />
        </div>
      </div>
    </section>
    
    <!-- 页脚 -->
    <MobileFooter 
      :show-back-to-top="true"
      :quick-links="footerLinks"
      copyright-text="© 2022-2023 SPINRED 版权所有"
      company-info="无锡市双马智能科技有限公司"
    />
  </div>
</template>

<script setup>
import MobileFooter from '../components/MobileFooter.vue'
import MobileHeader from '../components/MobileHeader.vue'
import NavigationCard from '../components/NavigationCard.vue'

// 页脚快速链接
const footerLinks = [
  { text: '公司简介', url: '/company/' },
  { text: '产品展示', url: '/products/' },
  { text: '联系我们', url: '/contact/' }
]

// 计算装饰线条样式
const getLineStyle = (index) => {
  const totalLines = 12
  const angle = (index - 1) * (180 / (totalLines - 1)) - 90 // -90度到90度
  const length = Math.abs(Math.cos(angle * Math.PI / 180)) * 100 + 50 // 50px到150px
  
  return {
    transform: `rotate(${angle}deg)`,
    width: `${length}px`,
    left: `${50 + (index - 6.5) * 8}%`, // 分布在中心两侧
    animationDelay: `${index * 0.1}s`
  }
}
</script>

<style lang="scss" scoped>
@use '../styles/mobile.scss' as mobile;

.home-page {
  background: var(--brand-secondary);
  min-height: 100vh;
}

.mobile-hero {
  background: var(--brand-secondary);
  color: var(--brand-white);
  text-align: center;
  padding: var(--spacing-16) var(--spacing-4) var(--spacing-12);
  position: relative;
  overflow: hidden;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .hero-content {
    position: relative;
    z-index: 2;
    max-width: 400px;
    margin: 0 auto;
  }
  
  .hero-logo-container {
    margin-bottom: var(--spacing-8);
  }
  
  .hero-logo {
    width: 120px;
    height: auto;
    filter: brightness(0) invert(1); // 将logo转为白色
  }
  
  .hero-title {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    margin-bottom: var(--spacing-4);
    letter-spacing: 2px;
    
    @include mobile.respond-to('md') {
      font-size: var(--font-size-4xl);
    }
  }
  
  .hero-subtitle {
    font-size: var(--font-size-lg);
    opacity: 0.9;
    margin-bottom: var(--spacing-12);
    font-weight: 300;
  }
  
  .hero-product {
    margin: var(--spacing-8) 0;
  }
  
  .hero-image {
    max-width: 100%;
    height: auto;
    max-height: 400px;
    filter: drop-shadow(0 10px 30px rgba(0, 0, 0, 0.3));
  }
}

// 装饰性背景
.hero-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  overflow: hidden;
  
  .decoration-lines {
    position: relative;
    width: 100%;
    height: 100%;
  }
  
  .decoration-line {
    position: absolute;
    top: 50%;
    height: 2px;
    background: var(--brand-white);
    opacity: 0.1;
    transform-origin: left center;
    animation: lineGlow 3s ease-in-out infinite;
    
    &:nth-child(odd) {
      animation-direction: alternate;
    }
  }
}

@keyframes lineGlow {
  0%, 100% {
    opacity: 0.1;
    transform: scaleX(0.8);
  }
  50% {
    opacity: 0.3;
    transform: scaleX(1);
  }
}

// 导航区域
.navigation-section {
  background: var(--brand-white);
  border-radius: var(--radius-xl) var(--radius-xl) 0 0;
  margin-top: -var(--spacing-6);
  position: relative;
  z-index: 3;
  
  .mobile-grid {
    gap: var(--spacing-6);
    
    @include respond-to('md') {
      gap: var(--spacing-8);
    }
  }
}

// 响应式调整
@media (max-width: 480px) {
  .mobile-hero {
    padding: var(--spacing-12) var(--spacing-3) var(--spacing-8);
    
    .hero-title {
      font-size: var(--font-size-2xl);
    }
    
    .hero-subtitle {
      font-size: var(--font-size-base);
    }
    
    .hero-logo {
      width: 100px;
    }
  }
  
  .navigation-section {
    .mobile-grid {
      gap: var(--spacing-4);
    }
  }
}

// 进入动画
.hero-content {
  animation: fadeInUp 1s ease-out;
}

.navigation-section {
  animation: slideInUp 0.8s ease-out 0.3s both;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
