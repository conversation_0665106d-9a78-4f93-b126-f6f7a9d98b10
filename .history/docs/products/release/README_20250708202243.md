---
layout: ProductPageLayout
pageTitle: 新品发布/New Product Releases
contentHeader: 新品发布/New Product Releases
heroImage:
  src: https://img-public.hui1688.cn/shuangma/new-release-hero.jpg
  alt: 双马智能科技新品发布预告
backUrl: /products/
showMoreContent: true
moreContentUrl: /products/
---

# 🚀 即将发布

双马智能科技即将推出革命性的新一代电动车仪表产品。我们的研发团队经过两年的精心研发，将为市场带来前所未有的创新体验。以下是即将发布的产品预览。

## 📅 发布时间线

### ✅ 产品研发完成 - 2023年10月
核心技术研发完成，产品原型测试通过

### ✅ 质量认证 - 2023年11月
通过CE、FCC、3C等国际质量认证

### 🔄 批量生产 - 2023年12月
正在进行批量生产，预计月产能10万台

### 🎯 正式发布 - 2024年1月
产品正式发布，开始市场销售

## 🔮 产品预览

### 🏆 双马 X1 智能仪表 (旗舰产品)
**ShuangMa X1 Smart Dashboard**

**核心特性：**
- 🎯 **AI智能识别**：集成人工智能算法，智能识别驾驶模式
- 📡 **5G物联网**：支持5G网络连接，实现车联网功能
- 🔋 **超长续航**：低功耗设计，待机时间超过30天

### 🌱 ECO环保系列
专注环保理念，采用可回收材料制造
- ♻️ 环保认证材料
- 🌿 可持续发展设计
- 💚 绿色制造工艺

### 🏁 运动竞技系列
为电动车竞技运动量身定制的专业仪表
- ⚡ 高速响应
- 🏆 竞技专用功能
- 📊 性能数据记录

## 💡 技术创新亮点

### 🧠 AI智能算法
首次在电动车仪表中集成AI算法，实现智能驾驶辅助功能

### 🌐 云端数据同步
支持云端数据同步，多设备无缝切换，数据永不丢失

### 🔒 安全加密
采用银行级加密技术，保护用户隐私和数据安全

### ⚡ 快速充电
支持无线充电和快速充电，15分钟充电可使用一周

## 🔔 发布通知订阅

想要第一时间了解新品发布信息？我们将在产品正式发布时第一时间通知您。

**感兴趣的产品：**
- ✅ 双马 X1 智能仪表
- ✅ ECO环保系列
- ✅ 运动竞技系列

**联系方式：**
- 📧 邮箱订阅：<EMAIL>
- 📱 微信关注：双马智能科技
- 📞 咨询热线：400-888-8888

## 🎉 预售优惠

**早鸟优惠：**
- 🎁 前100名预订用户享受8折优惠
- 🚚 免费包邮到家
- 🔧 免费安装服务
- 📞 专属客服支持

---

*关注我们的发布动态，成为第一批体验未来科技的用户！*
    <div class="preview-section">
      <h2>🔮 产品预览</h2>
      
      <!-- 旗舰产品 -->
      <div class="preview-item flagship">
        <div class="preview-header">
          <span class="flagship-badge">旗舰产品</span>
          <h3>双马 X1 智能仪表</h3>
          <p class="preview-subtitle">ShuangMa X1 Smart Dashboard</p>
        </div>
        
        <div class="preview-content">
          <div class="preview-image">
            <img 
              src="https://img-public.hui1688.cn/shuangma/x1-preview.jpg" 
              alt="双马 X1 智能仪表预览图" 
              class="preview-img"
            />
            <div class="coming-soon-overlay">
              <span>即将发布</span>
            </div>
          </div>
          
          <div class="preview-info">
            <div class="feature-highlights">
              <div class="highlight-item">
                <span class="highlight-icon">🎯</span>
                <div class="highlight-text">
                  <h4>AI智能识别</h4>
                  <p>集成人工智能算法，智能识别驾驶模式</p>
                </div>
              </div>
              
              <div class="highlight-item">
                <span class="highlight-icon">📡</span>
                <div class="highlight-text">
                  <h4>5G物联网</h4>
                  <p>支持5G网络连接，实现车联网功能</p>
                </div>
              </div>
              
              <div class="highlight-item">
                <span class="highlight-icon">🔋</span>
                <div class="highlight-text">
                  <h4>超长续航</h4>
                  <p>低功耗设计，待机时间超过30天</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 其他预览产品 -->
      <div class="preview-grid">
        <div class="preview-card">
          <div class="card-image">
            <img 
              src="https://img-public.hui1688.cn/shuangma/eco-series.jpg" 
              alt="ECO环保系列" 
              class="card-img"
            />
          </div>
          <div class="card-content">
            <h4>ECO环保系列</h4>
            <p>专注环保理念，采用可回收材料制造</p>
            <div class="release-status">
              <span class="status-badge eco">环保认证</span>
            </div>
          </div>
        </div>
        
        <div class="preview-card">
          <div class="card-image">
            <img 
              src="https://img-public.hui1688.cn/shuangma/sport-series.jpg" 
              alt="运动竞技系列" 
              class="card-img"
            />
          </div>
          <div class="card-content">
            <h4>运动竞技系列</h4>
            <p>为电动车竞技运动量身定制的专业仪表</p>
            <div class="release-status">
              <span class="status-badge sport">竞技专用</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 技术创新 -->
    <div class="content-section">
      <h2>💡 技术创新亮点</h2>
      <div class="innovation-grid">
        <div class="innovation-item">
          <div class="innovation-icon">🧠</div>
          <h4>AI智能算法</h4>
          <p>首次在电动车仪表中集成AI算法，实现智能驾驶辅助功能</p>
        </div>
        
        <div class="innovation-item">
          <div class="innovation-icon">🌐</div>
          <h4>云端数据同步</h4>
          <p>支持云端数据同步，多设备无缝切换，数据永不丢失</p>
        </div>
        
        <div class="innovation-item">
          <div class="innovation-icon">🔒</div>
          <h4>安全加密</h4>
          <p>采用银行级加密技术，保护用户隐私和数据安全</p>
        </div>
        
        <div class="innovation-item">
          <div class="innovation-icon">⚡</div>
          <h4>快速充电</h4>
          <p>支持无线充电和快速充电，15分钟充电可使用一周</p>
        </div>
      </div>
    </div>

    <!-- 预约通知 -->
    <div class="content-section notification-section">
      <h2>🔔 发布通知</h2>
      <p>
        想要第一时间了解新品发布信息？订阅我们的发布通知，我们将在产品正式发布时第一时间通知您。
      </p>
      
      <div class="notification-form">
        <div class="form-group">
          <label>邮箱地址</label>
          <input type="email" placeholder="请输入您的邮箱地址" />
        </div>
        <div class="form-group">
          <label>感兴趣的产品</label>
          <div class="checkbox-group">
            <label class="checkbox-item">
              <input type="checkbox" />
              <span>双马 X1 智能仪表</span>
            </label>
            <label class="checkbox-item">
              <input type="checkbox" />
              <span>ECO环保系列</span>
            </label>
            <label class="checkbox-item">
              <input type="checkbox" />
              <span>运动竞技系列</span>
            </label>
          </div>
        </div>
        <button class="subscribe-btn">订阅发布通知</button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.release-showcase {
  max-width: 100%;
}

/* 时间线样式 */
.timeline-section {
  margin: var(--spacing-8) 0;
}

.timeline {
  position: relative;
  padding-left: var(--spacing-8);
}

.timeline::before {
  content: '';
  position: absolute;
  left: var(--spacing-4);
  top: 0;
  bottom: 0;
  width: 2px;
  background: var(--brand-light-gray);
}

.timeline-item {
  position: relative;
  margin-bottom: var(--spacing-6);
}

.timeline-marker {
  position: absolute;
  left: calc(-1 * var(--spacing-4) - 6px);
  top: var(--spacing-2);
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--brand-light-gray);
  border: 2px solid var(--brand-white);
}

.timeline-item.completed .timeline-marker {
  background: var(--brand-success);
}

.timeline-item.active .timeline-marker {
  background: var(--brand-primary);
  animation: pulse 2s infinite;
}

.timeline-item.upcoming .timeline-marker {
  background: var(--brand-warning);
}

.timeline-content h3 {
  margin: 0 0 var(--spacing-1);
  color: var(--brand-dark-gray);
}

.timeline-date {
  font-size: var(--font-size-sm);
  color: var(--brand-primary);
  font-weight: 600;
  margin: 0 0 var(--spacing-2);
}

/* 预览产品样式 */
.preview-section {
  margin: var(--spacing-8) 0;
}

.flagship {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: var(--brand-white);
  border-radius: var(--radius-xl);
  padding: var(--spacing-8);
  margin-bottom: var(--spacing-8);
}

.flagship-badge {
  background: rgba(255, 255, 255, 0.2);
  padding: var(--spacing-1) var(--spacing-3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: 600;
}

.preview-header h3 {
  font-size: var(--font-size-2xl);
  margin: var(--spacing-4) 0 var(--spacing-2);
}

.preview-content {
  display: grid;
  gap: var(--spacing-6);
  margin-top: var(--spacing-6);
}

@media (min-width: 768px) {
  .preview-content {
    grid-template-columns: 1fr 1fr;
    align-items: center;
  }
}

.preview-image {
  position: relative;
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.coming-soon-overlay {
  position: absolute;
  top: var(--spacing-4);
  right: var(--spacing-4);
  background: rgba(255, 255, 255, 0.9);
  color: var(--brand-dark-gray);
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  font-weight: 600;
}

.feature-highlights {
  display: grid;
  gap: var(--spacing-4);
}

.highlight-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-3);
}

.highlight-icon {
  font-size: var(--font-size-xl);
  flex-shrink: 0;
}

.highlight-text h4 {
  margin: 0 0 var(--spacing-1);
  font-size: var(--font-size-lg);
}

.highlight-text p {
  margin: 0;
  opacity: 0.9;
}

/* 预览卡片网格 */
.preview-grid {
  display: grid;
  gap: var(--spacing-6);
  margin-top: var(--spacing-6);
}

@media (min-width: 768px) {
  .preview-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

.preview-card {
  background: var(--brand-white);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
}

.preview-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-md);
}

.card-image {
  height: 200px;
  overflow: hidden;
}

.card-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.card-content {
  padding: var(--spacing-6);
}

.card-content h4 {
  margin: 0 0 var(--spacing-2);
  color: var(--brand-dark-gray);
}

.status-badge {
  padding: var(--spacing-1) var(--spacing-3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: 600;
}

.status-badge.eco {
  background: var(--brand-success);
  color: var(--brand-white);
}

.status-badge.sport {
  background: var(--brand-warning);
  color: var(--brand-white);
}

/* 创新亮点 */
.innovation-grid {
  display: grid;
  gap: var(--spacing-6);
  margin-top: var(--spacing-6);
}

@media (min-width: 768px) {
  .innovation-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

.innovation-item {
  text-align: center;
  padding: var(--spacing-6);
  background: var(--brand-white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
}

.innovation-icon {
  font-size: var(--font-size-3xl);
  margin-bottom: var(--spacing-4);
}

.innovation-item h4 {
  margin: 0 0 var(--spacing-2);
  color: var(--brand-dark-gray);
}

/* 通知订阅 */
.notification-section {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: var(--spacing-8);
  border-radius: var(--radius-lg);
  margin-top: var(--spacing-8);
}

.notification-form {
  margin-top: var(--spacing-6);
  max-width: 400px;
}

.form-group {
  margin-bottom: var(--spacing-4);
}

.form-group label {
  display: block;
  margin-bottom: var(--spacing-2);
  font-weight: 600;
  color: var(--brand-dark-gray);
}

.form-group input[type="email"] {
  width: 100%;
  padding: var(--spacing-3);
  border: 1px solid var(--brand-light-gray);
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
}

.checkbox-group {
  display: grid;
  gap: var(--spacing-2);
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-weight: normal;
}

.subscribe-btn {
  background: var(--gradient-primary);
  color: var(--brand-white);
  border: none;
  padding: var(--spacing-3) var(--spacing-6);
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.subscribe-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

/* 动画 */
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
}
</style>
