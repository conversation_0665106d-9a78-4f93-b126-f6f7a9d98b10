---
layout: ProductPageLayout
pageTitle: 本月新品/New products this month
contentHeader: 本月新品/New products this month
heroImage:
  src: https://img-public.hui1688.cn/shuangma/thumb_6310248de1640.jpg
  alt: 双马智能科技本月新品展示
backUrl: /products/
showMoreContent: true
moreContentUrl: /products/
---
  <div class="product-showcase">
    <!-- 产品介绍 -->
    <div class="content-section">
      <h2>🆕 本月精选新品</h2>
      <p>
        双马智能科技本月推出多款创新电动车仪表产品，采用最新技术和设计理念，为电动车行业带来更优质的解决方案。我们的新品在功能性、稳定性和用户体验方面都有显著提升。
      </p>
    </div>

    <!-- 产品列表 -->
    <div class="product-grid">
      <!-- 鬼火指针版 -->
      <div class="product-item">
        <div class="product-image">
          <img 
            src="https://img-public.hui1688.cn/shuangma/product-ghost-fire.jpg" 
            alt="鬼火指针版电动车仪表" 
            class="product-img"
          />
        </div>
        <div class="product-info">
          <h3 class="product-title">鬼火指针版</h3>
          <p class="product-subtitle">Ghost Fire Pointer Edition</p>
          <div class="product-features">
            <span class="feature-tag">🔥 炫酷指针</span>
            <span class="feature-tag">💡 LED背光</span>
            <span class="feature-tag">⚡ 高精度</span>
          </div>
          <p class="product-description">
            采用独特的鬼火指针设计，配备高亮度LED背光显示，提供精准的速度和电量指示。适用于追求个性化的年轻用户群体。
          </p>
        </div>
      </div>

      <!-- 双马-2蓝牙版 -->
      <div class="product-item">
        <div class="product-image">
          <img 
            src="https://img-public.hui1688.cn/shuangma/product-bluetooth.jpg" 
            alt="双马-2蓝牙版电动车仪表" 
            class="product-img"
          />
        </div>
        <div class="product-info">
          <h3 class="product-title">双马-2蓝牙版</h3>
          <p class="product-subtitle">Double Horse -2 Bluetooth Version</p>
          <div class="product-features">
            <span class="feature-tag">📱 蓝牙连接</span>
            <span class="feature-tag">📊 数据同步</span>
            <span class="feature-tag">🔋 智能管理</span>
          </div>
          <p class="product-description">
            集成蓝牙4.0技术，支持手机APP连接，实时监控车辆状态，提供智能电池管理和行程记录功能。
          </p>
        </div>
      </div>

      <!-- 智能LCD显示版 -->
      <div class="product-item">
        <div class="product-image">
          <img 
            src="https://img-public.hui1688.cn/shuangma/product-lcd-smart.jpg" 
            alt="智能LCD显示版电动车仪表" 
            class="product-img"
          />
        </div>
        <div class="product-info">
          <h3 class="product-title">智能LCD显示版</h3>
          <p class="product-subtitle">Smart LCD Display Version</p>
          <div class="product-features">
            <span class="feature-tag">📺 高清LCD</span>
            <span class="feature-tag">🌡️ 温度显示</span>
            <span class="feature-tag">⏰ 时钟功能</span>
          </div>
          <p class="product-description">
            配备高分辨率LCD显示屏，支持多种信息显示模式，包括速度、电量、温度、时间等，界面清晰易读。
          </p>
        </div>
      </div>
    </div>

    <!-- 技术特点 -->
    <div class="content-section">
      <h2>🔧 技术特点</h2>
      <ul>
        <li><strong>先进工艺</strong>：采用最新的SMT贴片工艺，确保产品稳定性和可靠性</li>
        <li><strong>防水设计</strong>：IP65防护等级，适应各种恶劣天气条件</li>
        <li><strong>节能环保</strong>：低功耗设计，延长电池使用寿命</li>
        <li><strong>易于安装</strong>：标准化接口设计，兼容主流电动车型</li>
        <li><strong>质量保证</strong>：通过CE、FCC等国际认证，品质有保障</li>
      </ul>
    </div>

    <!-- 应用场景 -->
    <div class="content-section">
      <h2>🚲 适用车型</h2>
      <div class="application-grid">
        <div class="application-item">
          <span class="app-icon">🛵</span>
          <span class="app-text">电动摩托车</span>
        </div>
        <div class="application-item">
          <span class="app-icon">🚲</span>
          <span class="app-text">电动自行车</span>
        </div>
        <div class="application-item">
          <span class="app-icon">🛴</span>
          <span class="app-text">电动滑板车</span>
        </div>
        <div class="application-item">
          <span class="app-icon">🏍️</span>
          <span class="app-text">电动三轮车</span>
        </div>
      </div>
    </div>

    <!-- 联系信息 -->
    <div class="content-section contact-section">
      <h2>📞 了解更多</h2>
      <p>
        如需了解更多产品详情或获取技术支持，请联系我们的销售团队。我们将为您提供专业的产品咨询和定制化解决方案。
      </p>
      <div class="contact-info">
        <p><strong>销售热线</strong>：400-888-8888</p>
        <p><strong>技术支持</strong>：<EMAIL></p>
        <p><strong>在线咨询</strong>：工作日 9:00-18:00</p>
      </div>
    </div>
  </div>
</template>

<style scoped>
.product-showcase {
  max-width: 100%;
}

.product-grid {
  display: grid;
  gap: var(--spacing-8);
  margin: var(--spacing-8) 0;
}

.product-item {
  background: var(--brand-white);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
}

.product-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.product-image {
  width: 100%;
  height: 200px;
  overflow: hidden;
  background: var(--brand-light-gray);
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-info {
  padding: var(--spacing-6);
}

.product-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--brand-dark-gray);
  margin: 0 0 var(--spacing-2);
}

.product-subtitle {
  font-size: var(--font-size-sm);
  color: #666;
  margin: 0 0 var(--spacing-4);
  font-style: italic;
}

.product-features {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-4);
}

.feature-tag {
  background: var(--gradient-primary);
  color: var(--brand-white);
  padding: var(--spacing-1) var(--spacing-3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: 500;
}

.product-description {
  color: #555;
  line-height: 1.6;
  margin: 0;
}

.application-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-4);
  margin-top: var(--spacing-4);
}

@media (min-width: 768px) {
  .application-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

.application-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-4);
  background: var(--brand-white);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
}

.app-icon {
  font-size: var(--font-size-2xl);
  margin-bottom: var(--spacing-2);
}

.app-text {
  font-size: var(--font-size-sm);
  color: var(--brand-dark-gray);
  font-weight: 500;
}

.contact-section {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: var(--spacing-6);
  border-radius: var(--radius-lg);
  margin-top: var(--spacing-8);
}

.contact-info {
  margin-top: var(--spacing-4);
}

.contact-info p {
  margin-bottom: var(--spacing-2);
  color: var(--brand-dark-gray);
}
</style>
