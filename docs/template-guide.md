# 双马智能科技移动端模板系统使用指南

本文档详细介绍了基于VuePress 2.x构建的移动端优先模板系统的使用方法。

## 概述

该模板系统专为双马智能科技官网设计，采用移动端优先的响应式设计理念，提供了完整的页面模板和组件库。

### 特性

- 🚀 移动端优先的响应式设计
- 🎨 基于设计稿的精确还原
- 📱 触摸友好的交互体验
- 🔧 模块化组件架构
- ⚡ 优化的性能表现
- 🎯 SEO友好

## 页面布局模板

### 1. Home 布局

用于首页展示，包含品牌展示和导航卡片。

```markdown
---
layout: Home
title: 双马智能科技
description: 电动车仪表专业生产制造商
---
```

**特性：**
- 全屏英雄区域
- 品牌Logo和标题展示
- 产品图片展示
- 导航卡片网格
- 装饰性背景动画

### 2. CompanyProfile 布局

用于公司简介页面，支持图文混排。

```markdown
---
layout: CompanyProfile
title: 公司简介/Company Profile
description: 无锡市双马智能科技有限公司
---
```

**特性：**
- 移动端导航头部
- 页面标题区域
- 内容卡片布局
- 中英文内容支持
- 响应式图片展示

### 3. ProductDisplay 布局

用于产品展示页面，支持分类导航。

```markdown
---
layout: ProductDisplay
title: 产品展示
description: 双马智能科技产品系列展示
---
```

**特性：**
- 产品英雄区域
- 分类导航卡片
- 渐变色彩搭配
- 悬停动画效果

## 核心组件

### MobileHeader 组件

移动端页面头部组件，提供导航功能。

```vue
<MobileHeader 
  title="页面标题"
  :show-back="true"
  back-url="/"
  :show-logo="false"
  :show-home="true"
/>
```

**Props：**
- `title`: 页面标题
- `showBack`: 是否显示返回按钮
- `backUrl`: 返回链接
- `showLogo`: 是否显示Logo
- `showHome`: 是否显示首页按钮

### NavigationCard 组件

导航卡片组件，用于创建可点击的导航项。

```vue
<NavigationCard
  title="卡片标题"
  subtitle="副标题"
  icon-text="🏢"
  to="/company/"
  :show-arrow="true"
/>
```

**Props：**
- `title`: 卡片标题
- `subtitle`: 副标题
- `iconText`: 图标文本（emoji）
- `iconBgColor`: 图标背景色
- `to`: 路由链接
- `size`: 尺寸（small/normal/large）
- `variant`: 样式变体（default/primary/secondary）

### MobileFooter 组件

移动端页脚组件，提供返回顶部和快速链接。

```vue
<MobileFooter 
  :show-back-to-top="true"
  :quick-links="footerLinks"
  copyright-text="© 2022-2023 SPINRED 版权所有"
/>
```

**Props：**
- `showBackToTop`: 是否显示返回顶部
- `quickLinks`: 快速链接数组
- `copyrightText`: 版权文本

## 样式系统

### CSS变量

系统使用CSS变量定义设计令牌：

```scss
:root {
  --brand-primary: #e53e3e;
  --brand-secondary: #000000;
  --font-size-base: 1rem;
  --spacing-4: 1rem;
  --radius-lg: 0.75rem;
}
```

### 响应式断点

```scss
$breakpoints: (
  'xs': 0,
  'sm': 576px,
  'md': 768px,
  'lg': 992px,
  'xl': 1200px
);
```

### 工具类

```scss
.mobile-container  // 容器类
.mobile-section    // 区域类
.mobile-grid       // 网格布局
.btn              // 按钮基础类
.card             // 卡片基础类
```

## 创建新页面

### 1. 使用现有布局

```markdown
---
layout: CompanyProfile
title: 新页面标题
description: 页面描述
---

# 页面内容

这里是页面的具体内容...
```

### 2. 自定义布局

1. 在 `docs/.vuepress/layouts/` 创建新布局文件
2. 在 `docs/.vuepress/client.js` 注册布局
3. 在页面frontmatter中指定布局

## 最佳实践

### 1. 移动端优先

- 优先考虑移动端体验
- 使用触摸友好的交互元素
- 确保文字大小适合移动端阅读

### 2. 性能优化

- 优化图片大小和格式
- 使用懒加载
- 减少不必要的动画

### 3. 可访问性

- 提供适当的alt文本
- 确保足够的颜色对比度
- 支持键盘导航

## 文件结构

```
docs/
├── .vuepress/
│   ├── components/          # 组件目录
│   │   ├── MobileHeader.vue
│   │   ├── NavigationCard.vue
│   │   └── MobileFooter.vue
│   ├── layouts/            # 布局目录
│   │   ├── Home.vue
│   │   ├── CompanyProfile.vue
│   │   └── ProductDisplay.vue
│   ├── styles/             # 样式目录
│   │   ├── index.scss
│   │   └── mobile.scss
│   ├── config.js           # 配置文件
│   └── client.js           # 客户端配置
├── company/                # 公司简介
├── products/               # 产品展示
└── README.md               # 首页
```

## 部署说明

1. 构建项目：`npm run docs:build`
2. 部署dist目录到服务器
3. 确保服务器支持SPA路由

## 技术支持

如有问题，请参考：
- [VuePress 2.x 官方文档](https://v2.vuepress.vuejs.org/)
- [Vue 3 官方文档](https://vuejs.org/)
- [SCSS 文档](https://sass-lang.com/)
