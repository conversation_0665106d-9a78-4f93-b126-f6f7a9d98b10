---
layout: ProductPageLayout
pageTitle: 电摩系列/Electric Motorcycle Series
contentHeader: 电摩系列/Electric Motorcycle Series
heroImage:
  src: https://img-public.hui1688.cn/shuangma/electric-motorcycle-hero.jpg
  alt: 双马智能科技电摩系列仪表
backUrl: /products/
showMoreContent: true
moreContentUrl: /products/
---

<template #content>
  <div class="electric-series-showcase">
    <!-- 系列介绍 -->
    <div class="content-section">
      <h2>🏍️ 专为电动摩托车设计</h2>
      <p>
        双马智能科技电摩系列仪表专门为电动摩托车量身定制，具备高性能、高可靠性的特点。我们深入了解电动摩托车的使用场景和性能需求，为用户提供专业级的仪表解决方案。
      </p>
    </div>

    <!-- 产品特色 -->
    <div class="features-section">
      <h2>⭐ 产品特色</h2>
      <div class="features-grid">
        <div class="feature-card">
          <div class="feature-icon">🚀</div>
          <h3>高速响应</h3>
          <p>毫秒级数据刷新，实时显示车辆状态，确保骑行安全</p>
        </div>
        
        <div class="feature-card">
          <div class="feature-icon">💪</div>
          <h3>坚固耐用</h3>
          <p>军工级材料制造，抗震防水，适应各种恶劣环境</p>
        </div>
        
        <div class="feature-card">
          <div class="feature-icon">🎯</div>
          <h3>精准显示</h3>
          <p>高精度传感器，误差小于1%，数据准确可靠</p>
        </div>
        
        <div class="feature-card">
          <div class="feature-icon">🔧</div>
          <h3>易于安装</h3>
          <p>标准化接口设计，兼容99%的电动摩托车型</p>
        </div>
      </div>
    </div>

    <!-- 产品型号 -->
    <div class="models-section">
      <h2>🎯 产品型号</h2>
      
      <!-- 运动型 -->
      <div class="model-item sport">
        <div class="model-header">
          <div class="model-badge sport">运动型</div>
          <h3>SM-E1000 运动版</h3>
          <p class="model-subtitle">Sport Edition</p>
        </div>
        
        <div class="model-content">
          <div class="model-image">
            <img 
              src="https://img-public.hui1688.cn/shuangma/sm-e1000-sport.jpg" 
              alt="SM-E1000 运动版" 
              class="model-img"
            />
          </div>
          
          <div class="model-info">
            <div class="model-specs">
              <div class="spec-item">
                <span class="spec-label">最高时速显示</span>
                <span class="spec-value">120 km/h</span>
              </div>
              <div class="spec-item">
                <span class="spec-label">电压范围</span>
                <span class="spec-value">48V-72V</span>
              </div>
              <div class="spec-item">
                <span class="spec-label">显示模式</span>
                <span class="spec-value">数字+指针</span>
              </div>
              <div class="spec-item">
                <span class="spec-label">特殊功能</span>
                <span class="spec-value">运动模式切换</span>
              </div>
            </div>
            
            <div class="model-features">
              <span class="feature-tag">🏁 竞速模式</span>
              <span class="feature-tag">📊 性能监控</span>
              <span class="feature-tag">🔥 过热保护</span>
              <span class="feature-tag">⚡ 快速充电显示</span>
            </div>
            
            <p class="model-description">
              专为追求速度和性能的骑手设计，配备竞速模式和性能监控功能，让每一次骑行都充满激情。
            </p>
          </div>
        </div>
      </div>

      <!-- 豪华型 -->
      <div class="model-item luxury">
        <div class="model-header">
          <div class="model-badge luxury">豪华型</div>
          <h3>SM-E2000 豪华版</h3>
          <p class="model-subtitle">Luxury Edition</p>
        </div>
        
        <div class="model-content">
          <div class="model-image">
            <img 
              src="https://img-public.hui1688.cn/shuangma/sm-e2000-luxury.jpg" 
              alt="SM-E2000 豪华版" 
              class="model-img"
            />
          </div>
          
          <div class="model-info">
            <div class="model-specs">
              <div class="spec-item">
                <span class="spec-label">显示屏</span>
                <span class="spec-value">5.0" 彩色TFT</span>
              </div>
              <div class="spec-item">
                <span class="spec-label">连接方式</span>
                <span class="spec-value">蓝牙5.0 + WiFi</span>
              </div>
              <div class="spec-item">
                <span class="spec-label">导航功能</span>
                <span class="spec-value">GPS + 北斗</span>
              </div>
              <div class="spec-item">
                <span class="spec-label">语音控制</span>
                <span class="spec-value">支持</span>
              </div>
            </div>
            
            <div class="model-features">
              <span class="feature-tag">🗺️ GPS导航</span>
              <span class="feature-tag">📱 手机互联</span>
              <span class="feature-tag">🎵 音乐播放</span>
              <span class="feature-tag">☁️ 云端同步</span>
            </div>
            
            <p class="model-description">
              集成智能互联功能，支持导航、音乐、通话等多种功能，为高端用户提供豪华的骑行体验。
            </p>
          </div>
        </div>
      </div>

      <!-- 经济型 -->
      <div class="model-item economy">
        <div class="model-header">
          <div class="model-badge economy">经济型</div>
          <h3>SM-E500 标准版</h3>
          <p class="model-subtitle">Standard Edition</p>
        </div>
        
        <div class="model-content">
          <div class="model-image">
            <img 
              src="https://img-public.hui1688.cn/shuangma/sm-e500-standard.jpg" 
              alt="SM-E500 标准版" 
              class="model-img"
            />
          </div>
          
          <div class="model-info">
            <div class="model-specs">
              <div class="spec-item">
                <span class="spec-label">显示类型</span>
                <span class="spec-value">LED数码管</span>
              </div>
              <div class="spec-item">
                <span class="spec-label">基本功能</span>
                <span class="spec-value">速度/电量/里程</span>
              </div>
              <div class="spec-item">
                <span class="spec-label">防护等级</span>
                <span class="spec-value">IP65</span>
              </div>
              <div class="spec-item">
                <span class="spec-label">工作温度</span>
                <span class="spec-value">-30°C ~ +70°C</span>
              </div>
            </div>
            
            <div class="model-features">
              <span class="feature-tag">💰 性价比高</span>
              <span class="feature-tag">🔋 低功耗</span>
              <span class="feature-tag">🛡️ 稳定可靠</span>
              <span class="feature-tag">🔧 易维护</span>
            </div>
            
            <p class="model-description">
              提供核心的显示功能，性价比极高，适合对成本敏感的用户群体，质量稳定可靠。
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- 技术参数 -->
    <div class="content-section">
      <h2>📊 技术参数对比</h2>
      <div class="comparison-table">
        <div class="table-header">
          <div class="header-cell">功能特性</div>
          <div class="header-cell">运动版</div>
          <div class="header-cell">豪华版</div>
          <div class="header-cell">标准版</div>
        </div>
        
        <div class="table-row">
          <div class="cell">显示屏</div>
          <div class="cell">3.5" LCD</div>
          <div class="cell">5.0" TFT</div>
          <div class="cell">LED数码</div>
        </div>
        
        <div class="table-row">
          <div class="cell">蓝牙连接</div>
          <div class="cell">✅</div>
          <div class="cell">✅</div>
          <div class="cell">❌</div>
        </div>
        
        <div class="table-row">
          <div class="cell">GPS导航</div>
          <div class="cell">❌</div>
          <div class="cell">✅</div>
          <div class="cell">❌</div>
        </div>
        
        <div class="table-row">
          <div class="cell">语音控制</div>
          <div class="cell">❌</div>
          <div class="cell">✅</div>
          <div class="cell">❌</div>
        </div>
        
        <div class="table-row">
          <div class="cell">防水等级</div>
          <div class="cell">IP67</div>
          <div class="cell">IP67</div>
          <div class="cell">IP65</div>
        </div>
        
        <div class="table-row">
          <div class="cell">价格区间</div>
          <div class="cell">中等</div>
          <div class="cell">高端</div>
          <div class="cell">经济</div>
        </div>
      </div>
    </div>

    <!-- 安装指南 -->
    <div class="content-section">
      <h2>🔧 安装指南</h2>
      <div class="installation-steps">
        <div class="step-item">
          <div class="step-number">1</div>
          <div class="step-content">
            <h4>准备工作</h4>
            <p>确认车辆型号，准备安装工具，断开电源</p>
          </div>
        </div>
        
        <div class="step-item">
          <div class="step-number">2</div>
          <div class="step-content">
            <h4>拆卸原仪表</h4>
            <p>小心拆卸原有仪表，保留连接线束</p>
          </div>
        </div>
        
        <div class="step-item">
          <div class="step-number">3</div>
          <div class="step-content">
            <h4>连接新仪表</h4>
            <p>按照接线图连接新仪表，确保接触良好</p>
          </div>
        </div>
        
        <div class="step-item">
          <div class="step-number">4</div>
          <div class="step-content">
            <h4>测试功能</h4>
            <p>通电测试各项功能，确认显示正常</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 售后服务 -->
    <div class="content-section service-section">
      <h2>🛠️ 售后服务</h2>
      <div class="service-grid">
        <div class="service-item">
          <div class="service-icon">📞</div>
          <h4>技术支持</h4>
          <p>7×24小时技术热线</p>
          <p>400-888-8888</p>
        </div>
        
        <div class="service-item">
          <div class="service-icon">🔄</div>
          <h4>质保服务</h4>
          <p>2年免费质保</p>
          <p>终身维修服务</p>
        </div>
        
        <div class="service-item">
          <div class="service-icon">🚚</div>
          <h4>配送服务</h4>
          <p>全国包邮</p>
          <p>48小时内发货</p>
        </div>
        
        <div class="service-item">
          <div class="service-icon">💡</div>
          <h4>培训服务</h4>
          <p>免费安装培训</p>
          <p>在线视频教程</p>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.electric-series-showcase {
  max-width: 100%;
}

/* 特色功能网格 */
.features-grid {
  display: grid;
  gap: var(--spacing-6);
  margin-top: var(--spacing-6);
}

@media (min-width: 768px) {
  .features-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .features-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

.feature-card {
  text-align: center;
  padding: var(--spacing-6);
  background: var(--brand-white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-md);
}

.feature-icon {
  font-size: var(--font-size-3xl);
  margin-bottom: var(--spacing-4);
}

.feature-card h3 {
  margin: 0 0 var(--spacing-2);
  color: var(--brand-dark-gray);
}

/* 产品型号 */
.models-section {
  margin: var(--spacing-8) 0;
}

.model-item {
  background: var(--brand-white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  margin-bottom: var(--spacing-8);
  overflow: hidden;
}

.model-header {
  padding: var(--spacing-6);
  text-align: center;
}

.model-item.sport .model-header {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  color: var(--brand-white);
}

.model-item.luxury .model-header {
  background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
  color: var(--brand-white);
}

.model-item.economy .model-header {
  background: linear-gradient(135deg, #48dbfb 0%, #0abde3 100%);
  color: var(--brand-white);
}

.model-badge {
  display: inline-block;
  padding: var(--spacing-1) var(--spacing-3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: 600;
  margin-bottom: var(--spacing-2);
  background: rgba(255, 255, 255, 0.2);
}

.model-header h3 {
  margin: 0 0 var(--spacing-1);
  font-size: var(--font-size-xl);
}

.model-subtitle {
  margin: 0;
  opacity: 0.9;
  font-style: italic;
}

.model-content {
  display: grid;
  gap: var(--spacing-6);
  padding: var(--spacing-6);
}

@media (min-width: 768px) {
  .model-content {
    grid-template-columns: 1fr 2fr;
    align-items: center;
  }
}

.model-image {
  text-align: center;
}

.model-img {
  width: 100%;
  max-width: 300px;
  height: auto;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
}

.model-specs {
  display: grid;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-4);
}

.spec-item {
  display: flex;
  justify-content: space-between;
  padding: var(--spacing-2) 0;
  border-bottom: 1px solid var(--brand-light-gray);
}

.spec-label {
  font-weight: 500;
  color: var(--brand-dark-gray);
}

.spec-value {
  color: #666;
}

.model-features {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-4);
}

.feature-tag {
  background: var(--gradient-primary);
  color: var(--brand-white);
  padding: var(--spacing-1) var(--spacing-3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: 500;
}

.model-description {
  color: #555;
  line-height: 1.6;
  margin: 0;
}

/* 对比表格 */
.comparison-table {
  background: var(--brand-white);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  margin-top: var(--spacing-6);
}

.table-header {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  background: var(--gradient-primary);
  color: var(--brand-white);
}

.table-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  border-bottom: 1px solid var(--brand-light-gray);
}

.table-row:last-child {
  border-bottom: none;
}

.header-cell,
.cell {
  padding: var(--spacing-4);
  text-align: center;
}

.header-cell {
  font-weight: 600;
}

.cell:first-child {
  text-align: left;
  font-weight: 500;
  background: #f8f9fa;
}

/* 安装步骤 */
.installation-steps {
  display: grid;
  gap: var(--spacing-4);
  margin-top: var(--spacing-6);
}

.step-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-4);
  padding: var(--spacing-4);
  background: var(--brand-white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
}

.step-number {
  width: 40px;
  height: 40px;
  background: var(--gradient-primary);
  color: var(--brand-white);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  flex-shrink: 0;
}

.step-content h4 {
  margin: 0 0 var(--spacing-2);
  color: var(--brand-dark-gray);
}

.step-content p {
  margin: 0;
  color: #666;
}

/* 服务网格 */
.service-section {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: var(--spacing-8);
  border-radius: var(--radius-lg);
  margin-top: var(--spacing-8);
}

.service-grid {
  display: grid;
  gap: var(--spacing-6);
  margin-top: var(--spacing-6);
}

@media (min-width: 768px) {
  .service-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .service-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

.service-item {
  text-align: center;
  padding: var(--spacing-6);
  background: var(--brand-white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
}

.service-icon {
  font-size: var(--font-size-3xl);
  margin-bottom: var(--spacing-4);
}

.service-item h4 {
  margin: 0 0 var(--spacing-2);
  color: var(--brand-dark-gray);
}

.service-item p {
  margin-bottom: var(--spacing-1);
  color: #666;
}
</style>
