---
layout: ProductPageLayout
pageTitle: LCD系列/LCD Series
contentHeader: LCD系列/LCD Series
heroImage:
  src: https://img-public.hui1688.cn/shuangma/lcd-series-hero.jpg
  alt: 双马智能科技LCD系列电动车仪表
backUrl: /products/
showMoreContent: true
moreContentUrl: /products/
---

<template #content>
  <div class="lcd-showcase">
    <!-- 系列介绍 -->
    <div class="content-section">
      <h2>📱 LCD显示技术领先</h2>
      <p>
        双马智能科技LCD系列电动车仪表采用先进的液晶显示技术，提供高清晰度、高对比度的显示效果。我们的LCD系列产品在各种光照条件下都能保持优异的可读性，为用户提供最佳的视觉体验。
      </p>
    </div>

    <!-- 技术优势 -->
    <div class="advantages-section">
      <h2>🔬 技术优势</h2>
      <div class="advantages-grid">
        <div class="advantage-item">
          <div class="advantage-icon">🌟</div>
          <h3>高清显示</h3>
          <p>采用高分辨率LCD屏幕，显示内容清晰锐利，字体边缘平滑</p>
        </div>
        
        <div class="advantage-item">
          <div class="advantage-icon">☀️</div>
          <h3>强光可视</h3>
          <p>特殊的背光技术，在强烈阳光下依然保持清晰可见</p>
        </div>
        
        <div class="advantage-item">
          <div class="advantage-icon">🌡️</div>
          <h3>宽温工作</h3>
          <p>工作温度范围-40°C至+85°C，适应各种极端环境</p>
        </div>
        
        <div class="advantage-item">
          <div class="advantage-icon">⚡</div>
          <h3>低功耗</h3>
          <p>优化的电路设计，功耗比传统显示器降低30%</p>
        </div>
      </div>
    </div>

    <!-- 产品系列 -->
    <div class="product-series">
      <h2>🎯 产品系列</h2>
      
      <!-- 基础LCD系列 -->
      <div class="series-item">
        <div class="series-header">
          <h3>基础LCD系列</h3>
          <span class="series-badge basic">经济实用</span>
        </div>
        <div class="series-content">
          <div class="series-image">
            <img 
              src="https://img-public.hui1688.cn/shuangma/lcd-basic.jpg" 
              alt="基础LCD系列" 
              class="series-img"
            />
          </div>
          <div class="series-info">
            <div class="features-list">
              <div class="feature-item">
                <span class="feature-icon">📊</span>
                <span>单色LCD显示</span>
              </div>
              <div class="feature-item">
                <span class="feature-icon">🔋</span>
                <span>电量百分比显示</span>
              </div>
              <div class="feature-item">
                <span class="feature-icon">🏃</span>
                <span>速度实时显示</span>
              </div>
              <div class="feature-item">
                <span class="feature-icon">📏</span>
                <span>里程统计</span>
              </div>
            </div>
            <p class="series-description">
              适合对显示要求不高的基础电动车型，提供核心的速度、电量、里程显示功能，性价比极高。
            </p>
          </div>
        </div>
      </div>

      <!-- 彩色LCD系列 -->
      <div class="series-item">
        <div class="series-header">
          <h3>彩色LCD系列</h3>
          <span class="series-badge premium">高端精品</span>
        </div>
        <div class="series-content">
          <div class="series-image">
            <img 
              src="https://img-public.hui1688.cn/shuangma/lcd-color.jpg" 
              alt="彩色LCD系列" 
              class="series-img"
            />
          </div>
          <div class="series-info">
            <div class="features-list">
              <div class="feature-item">
                <span class="feature-icon">🌈</span>
                <span>全彩LCD显示</span>
              </div>
              <div class="feature-item">
                <span class="feature-icon">🎨</span>
                <span>多主题界面</span>
              </div>
              <div class="feature-item">
                <span class="feature-icon">📱</span>
                <span>蓝牙连接</span>
              </div>
              <div class="feature-item">
                <span class="feature-icon">🗺️</span>
                <span>GPS导航</span>
              </div>
            </div>
            <p class="series-description">
              采用全彩LCD屏幕，支持多种显示主题，集成蓝牙和GPS功能，为高端电动车提供完整的智能化解决方案。
            </p>
          </div>
        </div>
      </div>

      <!-- 触控LCD系列 -->
      <div class="series-item">
        <div class="series-header">
          <h3>触控LCD系列</h3>
          <span class="series-badge innovative">创新科技</span>
        </div>
        <div class="series-content">
          <div class="series-image">
            <img 
              src="https://img-public.hui1688.cn/shuangma/lcd-touch.jpg" 
              alt="触控LCD系列" 
              class="series-img"
            />
          </div>
          <div class="series-info">
            <div class="features-list">
              <div class="feature-item">
                <span class="feature-icon">👆</span>
                <span>电容触控屏</span>
              </div>
              <div class="feature-item">
                <span class="feature-icon">🎛️</span>
                <span>手势操作</span>
              </div>
              <div class="feature-item">
                <span class="feature-icon">🔧</span>
                <span>自定义设置</span>
              </div>
              <div class="feature-item">
                <span class="feature-icon">📞</span>
                <span>语音助手</span>
              </div>
            </div>
            <p class="series-description">
              革命性的触控操作体验，支持手势控制和语音交互，让电动车仪表操作更加直观便捷。
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- 技术规格 -->
    <div class="content-section">
      <h2>📋 技术规格</h2>
      <div class="specs-table">
        <div class="spec-row">
          <div class="spec-label">显示尺寸</div>
          <div class="spec-value">2.4" - 7.0" 可选</div>
        </div>
        <div class="spec-row">
          <div class="spec-label">分辨率</div>
          <div class="spec-value">240×320 至 800×480</div>
        </div>
        <div class="spec-row">
          <div class="spec-label">显示颜色</div>
          <div class="spec-value">单色/65K色/16M色</div>
        </div>
        <div class="spec-row">
          <div class="spec-label">视角</div>
          <div class="spec-value">170°(H) / 170°(V)</div>
        </div>
        <div class="spec-row">
          <div class="spec-label">亮度</div>
          <div class="spec-value">300-1000 cd/m²</div>
        </div>
        <div class="spec-row">
          <div class="spec-label">对比度</div>
          <div class="spec-value">1000:1 - 3000:1</div>
        </div>
        <div class="spec-row">
          <div class="spec-label">工作温度</div>
          <div class="spec-value">-40°C ~ +85°C</div>
        </div>
        <div class="spec-row">
          <div class="spec-label">防护等级</div>
          <div class="spec-value">IP65</div>
        </div>
      </div>
    </div>

    <!-- 应用案例 -->
    <div class="content-section">
      <h2>🚀 应用案例</h2>
      <div class="case-studies">
        <div class="case-item">
          <div class="case-icon">🏭</div>
          <h4>某知名电动车厂商</h4>
          <p>采用我们的彩色LCD系列，年销量突破50万台，用户满意度达98%</p>
        </div>
        
        <div class="case-item">
          <div class="case-icon">🚚</div>
          <h4>物流配送企业</h4>
          <p>使用基础LCD系列，降低成本30%，提高配送效率25%</p>
        </div>
        
        <div class="case-item">
          <div class="case-icon">🏍️</div>
          <h4>高端电摩品牌</h4>
          <p>选择触控LCD系列，打造差异化产品，市场占有率提升40%</p>
        </div>
      </div>
    </div>

    <!-- 定制服务 -->
    <div class="content-section custom-section">
      <h2>🎨 定制服务</h2>
      <p>
        我们提供专业的LCD仪表定制服务，可根据客户需求定制屏幕尺寸、显示内容、外观设计等。我们的工程师团队将与您密切合作，打造独一无二的产品解决方案。
      </p>
      
      <div class="custom-features">
        <div class="custom-item">
          <span class="custom-icon">🎯</span>
          <span>界面定制</span>
        </div>
        <div class="custom-item">
          <span class="custom-icon">🎨</span>
          <span>外观设计</span>
        </div>
        <div class="custom-item">
          <span class="custom-icon">⚙️</span>
          <span>功能开发</span>
        </div>
        <div class="custom-item">
          <span class="custom-icon">🔧</span>
          <span>硬件适配</span>
        </div>
      </div>
      
      <div class="contact-cta">
        <p><strong>联系我们的技术团队，开始您的定制之旅</strong></p>
        <p>技术热线：400-888-8888 转 2</p>
        <p>邮箱：<EMAIL></p>
      </div>
    </div>
  </div>
</template>

<style scoped>
.lcd-showcase {
  max-width: 100%;
}

/* 技术优势网格 */
.advantages-grid {
  display: grid;
  gap: var(--spacing-6);
  margin-top: var(--spacing-6);
}

@media (min-width: 768px) {
  .advantages-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .advantages-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

.advantage-item {
  text-align: center;
  padding: var(--spacing-6);
  background: var(--brand-white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
}

.advantage-item:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-md);
}

.advantage-icon {
  font-size: var(--font-size-3xl);
  margin-bottom: var(--spacing-4);
}

.advantage-item h3 {
  margin: 0 0 var(--spacing-2);
  color: var(--brand-dark-gray);
  font-size: var(--font-size-lg);
}

/* 产品系列 */
.product-series {
  margin: var(--spacing-8) 0;
}

.series-item {
  background: var(--brand-white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  margin-bottom: var(--spacing-8);
  overflow: hidden;
}

.series-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-6);
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid var(--brand-light-gray);
}

.series-header h3 {
  margin: 0;
  color: var(--brand-dark-gray);
  font-size: var(--font-size-xl);
}

.series-badge {
  padding: var(--spacing-1) var(--spacing-3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: 600;
}

.series-badge.basic {
  background: var(--brand-success);
  color: var(--brand-white);
}

.series-badge.premium {
  background: var(--brand-primary);
  color: var(--brand-white);
}

.series-badge.innovative {
  background: var(--brand-warning);
  color: var(--brand-white);
}

.series-content {
  display: grid;
  gap: var(--spacing-6);
  padding: var(--spacing-6);
}

@media (min-width: 768px) {
  .series-content {
    grid-template-columns: 1fr 2fr;
    align-items: center;
  }
}

.series-image {
  text-align: center;
}

.series-img {
  width: 100%;
  max-width: 300px;
  height: auto;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
}

.features-list {
  display: grid;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-4);
}

@media (min-width: 768px) {
  .features-list {
    grid-template-columns: repeat(2, 1fr);
  }
}

.feature-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--font-size-sm);
}

.feature-icon {
  font-size: var(--font-size-base);
}

.series-description {
  color: #666;
  line-height: 1.6;
  margin: 0;
}

/* 技术规格表 */
.specs-table {
  background: var(--brand-white);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  margin-top: var(--spacing-6);
}

.spec-row {
  display: grid;
  grid-template-columns: 1fr 2fr;
  border-bottom: 1px solid var(--brand-light-gray);
}

.spec-row:last-child {
  border-bottom: none;
}

.spec-label {
  padding: var(--spacing-4);
  background: #f8f9fa;
  font-weight: 600;
  color: var(--brand-dark-gray);
}

.spec-value {
  padding: var(--spacing-4);
  color: #555;
}

/* 应用案例 */
.case-studies {
  display: grid;
  gap: var(--spacing-6);
  margin-top: var(--spacing-6);
}

@media (min-width: 768px) {
  .case-studies {
    grid-template-columns: repeat(3, 1fr);
  }
}

.case-item {
  text-align: center;
  padding: var(--spacing-6);
  background: var(--brand-white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
}

.case-icon {
  font-size: var(--font-size-3xl);
  margin-bottom: var(--spacing-4);
}

.case-item h4 {
  margin: 0 0 var(--spacing-2);
  color: var(--brand-dark-gray);
}

/* 定制服务 */
.custom-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: var(--brand-white);
  padding: var(--spacing-8);
  border-radius: var(--radius-lg);
  margin-top: var(--spacing-8);
}

.custom-features {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-4);
  margin: var(--spacing-6) 0;
}

@media (min-width: 768px) {
  .custom-features {
    grid-template-columns: repeat(4, 1fr);
  }
}

.custom-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-4);
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-md);
}

.custom-icon {
  font-size: var(--font-size-xl);
}

.contact-cta {
  margin-top: var(--spacing-6);
  text-align: center;
  padding: var(--spacing-6);
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
}

.contact-cta p {
  margin-bottom: var(--spacing-2);
}
</style>
