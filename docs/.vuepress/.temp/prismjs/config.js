import "/Users/<USER>/code/work/shuangma/portal/node_modules/.pnpm/@vuepress+highlighter-helper@2.0.0-rc.85_@vueuse+core@13.5.0_vue@3.5.17__vuepress@2.0.0_cfed8dfed5ccb7c1d6b3be3106e5cae5/node_modules/@vuepress/highlighter-helper/lib/client/styles/base.css"
import "/Users/<USER>/code/work/shuangma/portal/node_modules/.pnpm/@vuepress+plugin-prismjs@2.0.0-rc.86_@vueuse+core@13.5.0_vue@3.5.17__vuepress@2.0.0-rc._c8ae1422d8968fe63645825bed0d975c/node_modules/@vuepress/plugin-prismjs/lib/client/styles/nord.css"
import "/Users/<USER>/code/work/shuangma/portal/node_modules/.pnpm/@vuepress+highlighter-helper@2.0.0-rc.85_@vueuse+core@13.5.0_vue@3.5.17__vuepress@2.0.0_cfed8dfed5ccb7c1d6b3be3106e5cae5/node_modules/@vuepress/highlighter-helper/lib/client/styles/line-numbers.css"
import "/Users/<USER>/code/work/shuangma/portal/node_modules/.pnpm/@vuepress+highlighter-helper@2.0.0-rc.85_@vueuse+core@13.5.0_vue@3.5.17__vuepress@2.0.0_cfed8dfed5ccb7c1d6b3be3106e5cae5/node_modules/@vuepress/highlighter-helper/lib/client/styles/notation-highlight.css"
import "/Users/<USER>/code/work/shuangma/portal/node_modules/.pnpm/@vuepress+highlighter-helper@2.0.0-rc.85_@vueuse+core@13.5.0_vue@3.5.17__vuepress@2.0.0_cfed8dfed5ccb7c1d6b3be3106e5cae5/node_modules/@vuepress/highlighter-helper/lib/client/styles/collapsed-lines.css"
import { setupCollapsedLines } from "/Users/<USER>/code/work/shuangma/portal/node_modules/.pnpm/@vuepress+highlighter-helper@2.0.0-rc.85_@vueuse+core@13.5.0_vue@3.5.17__vuepress@2.0.0_cfed8dfed5ccb7c1d6b3be3106e5cae5/node_modules/@vuepress/highlighter-helper/lib/client/index.js"
import "/Users/<USER>/code/work/shuangma/portal/node_modules/.pnpm/@vuepress+highlighter-helper@2.0.0-rc.85_@vueuse+core@13.5.0_vue@3.5.17__vuepress@2.0.0_cfed8dfed5ccb7c1d6b3be3106e5cae5/node_modules/@vuepress/highlighter-helper/lib/client/styles/code-block-title.css"

export default {
  setup() {
    setupCollapsedLines()
  }
}
