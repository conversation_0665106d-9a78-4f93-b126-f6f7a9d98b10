import comp from "/Users/<USER>/code/work/shuangma/portal/docs/.vuepress/.temp/pages/template-guide.html.vue"
const data = JSON.parse("{\"path\":\"/template-guide.html\",\"title\":\"双马智能科技移动端模板系统使用指南\",\"lang\":\"zh-CN\",\"frontmatter\":{},\"headers\":[{\"level\":2,\"title\":\"概述\",\"slug\":\"概述\",\"link\":\"#概述\",\"children\":[{\"level\":3,\"title\":\"特性\",\"slug\":\"特性\",\"link\":\"#特性\",\"children\":[]}]},{\"level\":2,\"title\":\"页面布局模板\",\"slug\":\"页面布局模板\",\"link\":\"#页面布局模板\",\"children\":[{\"level\":3,\"title\":\"1. Home 布局\",\"slug\":\"_1-home-布局\",\"link\":\"#_1-home-布局\",\"children\":[]},{\"level\":3,\"title\":\"2. CompanyProfile 布局\",\"slug\":\"_2-companyprofile-布局\",\"link\":\"#_2-companyprofile-布局\",\"children\":[]},{\"level\":3,\"title\":\"3. ProductDisplay 布局\",\"slug\":\"_3-productdisplay-布局\",\"link\":\"#_3-productdisplay-布局\",\"children\":[]}]},{\"level\":2,\"title\":\"核心组件\",\"slug\":\"核心组件\",\"link\":\"#核心组件\",\"children\":[{\"level\":3,\"title\":\"MobileHeader 组件\",\"slug\":\"mobileheader-组件\",\"link\":\"#mobileheader-组件\",\"children\":[]},{\"level\":3,\"title\":\"NavigationCard 组件\",\"slug\":\"navigationcard-组件\",\"link\":\"#navigationcard-组件\",\"children\":[]},{\"level\":3,\"title\":\"MobileFooter 组件\",\"slug\":\"mobilefooter-组件\",\"link\":\"#mobilefooter-组件\",\"children\":[]}]},{\"level\":2,\"title\":\"样式系统\",\"slug\":\"样式系统\",\"link\":\"#样式系统\",\"children\":[{\"level\":3,\"title\":\"CSS变量\",\"slug\":\"css变量\",\"link\":\"#css变量\",\"children\":[]},{\"level\":3,\"title\":\"响应式断点\",\"slug\":\"响应式断点\",\"link\":\"#响应式断点\",\"children\":[]},{\"level\":3,\"title\":\"工具类\",\"slug\":\"工具类\",\"link\":\"#工具类\",\"children\":[]}]},{\"level\":2,\"title\":\"创建新页面\",\"slug\":\"创建新页面\",\"link\":\"#创建新页面\",\"children\":[{\"level\":3,\"title\":\"1. 使用现有布局\",\"slug\":\"_1-使用现有布局\",\"link\":\"#_1-使用现有布局\",\"children\":[]},{\"level\":3,\"title\":\"2. 自定义布局\",\"slug\":\"_2-自定义布局\",\"link\":\"#_2-自定义布局\",\"children\":[]}]},{\"level\":2,\"title\":\"最佳实践\",\"slug\":\"最佳实践\",\"link\":\"#最佳实践\",\"children\":[{\"level\":3,\"title\":\"1. 移动端优先\",\"slug\":\"_1-移动端优先\",\"link\":\"#_1-移动端优先\",\"children\":[]},{\"level\":3,\"title\":\"2. 性能优化\",\"slug\":\"_2-性能优化\",\"link\":\"#_2-性能优化\",\"children\":[]},{\"level\":3,\"title\":\"3. 可访问性\",\"slug\":\"_3-可访问性\",\"link\":\"#_3-可访问性\",\"children\":[]}]},{\"level\":2,\"title\":\"文件结构\",\"slug\":\"文件结构\",\"link\":\"#文件结构\",\"children\":[]},{\"level\":2,\"title\":\"部署说明\",\"slug\":\"部署说明\",\"link\":\"#部署说明\",\"children\":[]},{\"level\":2,\"title\":\"技术支持\",\"slug\":\"技术支持\",\"link\":\"#技术支持\",\"children\":[]}],\"git\":{},\"filePathRelative\":\"template-guide.md\",\"excerpt\":\"\\n<p>本文档详细介绍了基于VuePress 2.x构建的移动端优先模板系统的使用方法。</p>\\n<h2>概述</h2>\\n<p>该模板系统专为双马智能科技官网设计，采用移动端优先的响应式设计理念，提供了完整的页面模板和组件库。</p>\\n<h3>特性</h3>\\n<ul>\\n<li>🚀 移动端优先的响应式设计</li>\\n<li>🎨 基于设计稿的精确还原</li>\\n<li>📱 触摸友好的交互体验</li>\\n<li>🔧 模块化组件架构</li>\\n<li>⚡ 优化的性能表现</li>\\n<li>🎯 SEO友好</li>\\n</ul>\\n<h2>页面布局模板</h2>\\n<h3>1. Home 布局</h3>\"}")
export { comp, data }

if (import.meta.webpackHot) {
  import.meta.webpackHot.accept()
  if (__VUE_HMR_RUNTIME__.updatePageData) {
    __VUE_HMR_RUNTIME__.updatePageData(data)
  }
}

if (import.meta.hot) {
  import.meta.hot.accept(({ data }) => {
    __VUE_HMR_RUNTIME__.updatePageData(data)
  })
}
