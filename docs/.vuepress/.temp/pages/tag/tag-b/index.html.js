import comp from "/Users/<USER>/code/work/shuangma/portal/docs/.vuepress/.temp/pages/tag/tag-b/index.html.vue"
const data = JSON.parse("{\"path\":\"/tag/tag-b/\",\"title\":\"Tag tag B\",\"lang\":\"zh-CN\",\"frontmatter\":{\"title\":\"Tag tag B\",\"sidebar\":false,\"blog\":{\"type\":\"category\",\"name\":\"tag B\",\"key\":\"tag\"},\"layout\":\"Tag\"},\"headers\":[],\"git\":{},\"filePathRelative\":null,\"excerpt\":\"\"}")
export { comp, data }

if (import.meta.webpackHot) {
  import.meta.webpackHot.accept()
  if (__VUE_HMR_RUNTIME__.updatePageData) {
    __VUE_HMR_RUNTIME__.updatePageData(data)
  }
}

if (import.meta.hot) {
  import.meta.hot.accept(({ data }) => {
    __VUE_HMR_RUNTIME__.updatePageData(data)
  })
}
