import comp from "/Users/<USER>/code/work/shuangma/portal/docs/.vuepress/.temp/pages/qualification/index.html.vue"
const data = JSON.parse("{\"path\":\"/qualification/\",\"title\":\"资质证书\",\"lang\":\"zh-CN\",\"frontmatter\":{\"title\":\"资质证书\",\"description\":\"双马智能科技资质证书和认证\"},\"headers\":[{\"level\":2,\"title\":\"企业资质\",\"slug\":\"企业资质\",\"link\":\"#企业资质\",\"children\":[]},{\"level\":2,\"title\":\"产品认证\",\"slug\":\"产品认证\",\"link\":\"#产品认证\",\"children\":[]},{\"level\":2,\"title\":\"知识产权\",\"slug\":\"知识产权\",\"link\":\"#知识产权\",\"children\":[]},{\"level\":2,\"title\":\"荣誉奖项\",\"slug\":\"荣誉奖项\",\"link\":\"#荣誉奖项\",\"children\":[]}],\"git\":{},\"filePathRelative\":\"qualification/README.md\",\"excerpt\":\"\\n<p>双马智能科技拥有完善的资质认证体系，确保产品质量和企业信誉。</p>\\n<h2>企业资质</h2>\\n<ul>\\n<li>高新技术企业认定证书</li>\\n<li>ISO9001质量管理体系认证</li>\\n<li>ISO14001环境管理体系认证</li>\\n<li>OHSAS18001职业健康安全管理体系认证</li>\\n</ul>\\n<h2>产品认证</h2>\\n<ul>\\n<li>3C强制性产品认证</li>\\n<li>CE欧盟认证</li>\\n<li>FCC美国联邦通信委员会认证</li>\\n<li>RoHS环保认证</li>\\n</ul>\\n<h2>知识产权</h2>\\n<ul>\\n<li>多项发明专利</li>\\n<li>实用新型专利</li>\\n<li>外观设计专利</li>\\n<li>软件著作权</li>\\n</ul>\"}")
export { comp, data }

if (import.meta.webpackHot) {
  import.meta.webpackHot.accept()
  if (__VUE_HMR_RUNTIME__.updatePageData) {
    __VUE_HMR_RUNTIME__.updatePageData(data)
  }
}

if (import.meta.hot) {
  import.meta.hot.accept(({ data }) => {
    __VUE_HMR_RUNTIME__.updatePageData(data)
  })
}
