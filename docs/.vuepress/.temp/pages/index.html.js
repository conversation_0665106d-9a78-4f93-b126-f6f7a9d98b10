import comp from "/Users/<USER>/code/work/shuangma/portal/docs/.vuepress/.temp/pages/index.html.vue"
const data = JSON.parse("{\"path\":\"/\",\"title\":\"双马智能科技\",\"lang\":\"zh-CN\",\"frontmatter\":{\"layout\":\"Home\",\"title\":\"双马智能科技\",\"description\":\"电动车仪表专业生产制造商\"},\"headers\":[],\"git\":{},\"filePathRelative\":\"README.md\",\"excerpt\":\"\"}")
export { comp, data }

if (import.meta.webpackHot) {
  import.meta.webpackHot.accept()
  if (__VUE_HMR_RUNTIME__.updatePageData) {
    __VUE_HMR_RUNTIME__.updatePageData(data)
  }
}

if (import.meta.hot) {
  import.meta.hot.accept(({ data }) => {
    __VUE_HMR_RUNTIME__.updatePageData(data)
  })
}
