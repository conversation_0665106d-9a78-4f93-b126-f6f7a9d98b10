import { CodeTabs } from "/Users/<USER>/code/work/shuangma/portal/node_modules/.pnpm/@vuepress+plugin-markdown-tab@2.0.0-rc.86_markdown-it@14.1.0_vuepress@2.0.0-rc.20_@vuep_0dc16c8964eb7cca9cb79a02a44cc5b2/node_modules/@vuepress/plugin-markdown-tab/lib/client/components/CodeTabs.js";
import { Tabs } from "/Users/<USER>/code/work/shuangma/portal/node_modules/.pnpm/@vuepress+plugin-markdown-tab@2.0.0-rc.86_markdown-it@14.1.0_vuepress@2.0.0-rc.20_@vuep_0dc16c8964eb7cca9cb79a02a44cc5b2/node_modules/@vuepress/plugin-markdown-tab/lib/client/components/Tabs.js";
import "/Users/<USER>/code/work/shuangma/portal/node_modules/.pnpm/@vuepress+plugin-markdown-tab@2.0.0-rc.86_markdown-it@14.1.0_vuepress@2.0.0-rc.20_@vuep_0dc16c8964eb7cca9cb79a02a44cc5b2/node_modules/@vuepress/plugin-markdown-tab/lib/client/styles/vars.css";

export default {
  enhance: ({ app }) => {
    app.component("CodeTabs", CodeTabs);
    app.component("Tabs", Tabs);
  },
};
