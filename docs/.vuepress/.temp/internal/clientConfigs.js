import * as clientConfig0 from '/Users/<USER>/code/work/shuangma/portal/node_modules/.pnpm/@vuepress+plugin-active-header-links@2.0.0-rc.86_vuepress@2.0.0-rc.20_@vuepress+bundler_d9707846c21920f09214918809683f3a/node_modules/@vuepress/plugin-active-header-links/lib/client/config.js'
import * as clientConfig1 from '/Users/<USER>/code/work/shuangma/portal/node_modules/.pnpm/@vuepress+plugin-copy-code@2.0.0-rc.86_vuepress@2.0.0-rc.20_@vuepress+bundler-vite@2.0._009a84860bf74b6c765a89d9063c995d/node_modules/@vuepress/plugin-copy-code/lib/client/config.js'
import * as clientConfig2 from '/Users/<USER>/code/work/shuangma/portal/node_modules/.pnpm/@vuepress+plugin-markdown-hint@2.0.0-rc.86_markdown-it@14.1.0_vue@3.5.17_vuepress@2.0.0_930fd0c8ba11b7cfe073693ce7342da8/node_modules/@vuepress/plugin-markdown-hint/lib/client/config.js'
import * as clientConfig3 from '/Users/<USER>/code/work/shuangma/portal/docs/.vuepress/.temp/git/config.js'
import * as clientConfig4 from '/Users/<USER>/code/work/shuangma/portal/node_modules/.pnpm/@vuepress+plugin-medium-zoom@2.0.0-rc.86_vuepress@2.0.0-rc.20_@vuepress+bundler-vite@2._115561c265a7310d6681fe7e771daa42/node_modules/@vuepress/plugin-medium-zoom/lib/client/config.js'
import * as clientConfig5 from '/Users/<USER>/code/work/shuangma/portal/node_modules/.pnpm/@vuepress+plugin-nprogress@2.0.0-rc.86_vuepress@2.0.0-rc.20_@vuepress+bundler-vite@2.0._851459fca24b954bec8d8bcdfc5d9d9a/node_modules/@vuepress/plugin-nprogress/lib/client/config.js'
import * as clientConfig6 from '/Users/<USER>/code/work/shuangma/portal/docs/.vuepress/.temp/prismjs/config.js'
import * as clientConfig7 from '/Users/<USER>/code/work/shuangma/portal/docs/.vuepress/.temp/markdown-tab/config.js'
import * as clientConfig8 from '/Users/<USER>/code/work/shuangma/portal/node_modules/.pnpm/@vuepress+plugin-theme-data@2.0.0-rc.86_vuepress@2.0.0-rc.20_@vuepress+bundler-vite@2.0_174ea93de6487fa5f17e228952930523/node_modules/@vuepress/plugin-theme-data/lib/client/config.js'
import * as clientConfig9 from '/Users/<USER>/code/work/shuangma/portal/node_modules/.pnpm/@vuepress+theme-default@2.0.0-rc.88_markdown-it@14.1.0_sass-embedded@1.89.2_vuepress@2._49b4648043aac59993924a5a7845e4c3/node_modules/@vuepress/theme-default/lib/client/config.js'
import * as clientConfig10 from '/Users/<USER>/code/work/shuangma/portal/docs/.vuepress/client.js'

export const clientConfigs = [
  clientConfig0,
  clientConfig1,
  clientConfig2,
  clientConfig3,
  clientConfig4,
  clientConfig5,
  clientConfig6,
  clientConfig7,
  clientConfig8,
  clientConfig9,
  clientConfig10,
].map((m) => m.default).filter(Boolean)
