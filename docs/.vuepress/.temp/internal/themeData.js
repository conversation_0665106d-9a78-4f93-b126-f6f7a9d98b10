export const themeData = JSON.parse("{\"logo\":\"https://img-public.hui1688.cn/shuangma/logo.jpg\",\"logoDark\":\"https://img-public.hui1688.cn/shuangma/logo.jpg\",\"navbar\":[\"/\",{\"text\":\"公司简介\",\"link\":\"/company/\"},{\"text\":\"产品展示\",\"link\":\"/products/\"},{\"text\":\"生产车间\",\"link\":\"/workshop/\"},{\"text\":\"资质证书\",\"link\":\"/qualification/\"},{\"text\":\"文章\",\"link\":\"/article/\"}],\"sidebar\":false,\"sidebarDepth\":0,\"editLink\":false,\"lastUpdated\":false,\"contributors\":false,\"locales\":{\"/\":{\"selectLanguageName\":\"English\"}},\"colorMode\":\"auto\",\"colorModeSwitch\":true,\"repo\":null,\"selectLanguageText\":\"Languages\",\"selectLanguageAriaLabel\":\"Select language\",\"editLinkText\":\"Edit this page\",\"contributorsText\":\"Contributors\",\"notFound\":[\"There's nothing here.\",\"How did we get here?\",\"That's a Four-Oh-Four.\",\"Looks like we've got some broken links.\"],\"backToHome\":\"Take me home\",\"openInNewWindow\":\"open in new window\",\"toggleColorMode\":\"toggle color mode\",\"toggleSidebar\":\"toggle sidebar\"}")

if (import.meta.webpackHot) {
  import.meta.webpackHot.accept()
  if (__VUE_HMR_RUNTIME__.updateThemeData) {
    __VUE_HMR_RUNTIME__.updateThemeData(themeData)
  }
}

if (import.meta.hot) {
  import.meta.hot.accept(({ themeData }) => {
    __VUE_HMR_RUNTIME__.updateThemeData(themeData)
  })
}
