<template>
  <footer class="mobile-footer">
    <div class="footer-content">
      <!-- 返回顶部按钮 -->
      <div v-if="showBackToTop" class="footer-actions">
        <button 
          class="back-to-top-btn"
          @click="scrollToTop"
          :class="{ 'visible': showBackToTopBtn }"
        >
          ↑ 返回顶部
        </button>
      </div>
      
      <!-- 更多内容按钮 -->
      <div v-if="showMoreContent" class="footer-actions">
        <RouteLink
          :to="moreContentUrl"
          class="more-content-btn"
        >
          更多精彩内容
        </RouteLink>
      </div>

      <!-- 快速导航链接 -->
      <div v-if="quickLinks.length > 0" class="footer-links">
        <RouteLink
          v-for="link in quickLinks"
          :key="link.text"
          :to="link.url"
          class="footer-link"
        >
          {{ link.text }}
        </RouteLink>
      </div>
      
      <!-- 版权信息 -->
      <div class="footer-copyright">
        <p class="copyright-text">{{ copyrightText }}</p>
        <p v-if="companyInfo" class="company-info">{{ companyInfo }}</p>
      </div>
    </div>
  </footer>
</template>

<script setup>
import { onMounted, onUnmounted, ref } from 'vue'
import { RouteLink } from 'vuepress/client'

// Props定义
const props = defineProps({
  // 是否显示返回顶部按钮
  showBackToTop: {
    type: Boolean,
    default: true
  },
  // 是否显示更多内容按钮
  showMoreContent: {
    type: Boolean,
    default: false
  },
  // 更多内容链接
  moreContentUrl: {
    type: String,
    default: '/articles/'
  },
  // 快速导航链接
  quickLinks: {
    type: Array,
    default: () => []
  },
  // 版权文本
  copyrightText: {
    type: String,
    default: '© 2022-2023 SPINRED 版权所有'
  },
  // 公司信息
  companyInfo: {
    type: String,
    default: '无锡市双马智能科技有限公司'
  }
})

// 响应式数据
const showBackToTopBtn = ref(false)

// 滚动到顶部
const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  })
}

// 监听滚动事件
const handleScroll = () => {
  showBackToTopBtn.value = window.scrollY > 300
}

// 生命周期
onMounted(() => {
  if (props.showBackToTop) {
    window.addEventListener('scroll', handleScroll)
    handleScroll() // 初始检查
  }
})

onUnmounted(() => {
  if (props.showBackToTop) {
    window.removeEventListener('scroll', handleScroll)
  }
})
</script>

<style lang="scss" scoped>
.mobile-footer {
  background: var(--brand-dark-gray);
  color: var(--brand-white);
  padding: var(--spacing-8) var(--spacing-4) var(--spacing-6);
  margin-top: var(--spacing-12);
  
  .footer-content {
    max-width: 600px;
    margin: 0 auto;
    text-align: center;
  }
}

.footer-actions {
  margin-bottom: var(--spacing-6);
  
  &:last-child {
    margin-bottom: 0;
  }
}

.back-to-top-btn {
  background: var(--gradient-primary);
  color: var(--brand-white);
  border: none;
  padding: var(--spacing-3) var(--spacing-6);
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-normal);
  opacity: 0;
  transform: translateY(10px);
  
  &.visible {
    opacity: 1;
    transform: translateY(0);
  }
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
  }
  
  &:active {
    transform: translateY(0);
  }
}

.more-content-btn {
  display: inline-block;
  background: transparent;
  color: var(--brand-white);
  border: 2px solid var(--brand-white);
  padding: var(--spacing-3) var(--spacing-6);
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  font-weight: 500;
  text-decoration: none;
  transition: all var(--transition-normal);
  
  &:hover {
    background: var(--brand-white);
    color: var(--brand-dark-gray);
    transform: translateY(-1px);
  }
}

.footer-links {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: var(--spacing-4) var(--spacing-6);
  margin-bottom: var(--spacing-6);
  
  .footer-link {
    color: var(--brand-white);
    text-decoration: none;
    font-size: var(--font-size-sm);
    opacity: 0.8;
    transition: all var(--transition-normal);
    position: relative;
    
    &:hover {
      opacity: 1;
      color: var(--brand-primary);
    }
    
    &::after {
      content: '';
      position: absolute;
      bottom: -2px;
      left: 0;
      width: 0;
      height: 1px;
      background: var(--brand-primary);
      transition: width var(--transition-normal);
    }
    
    &:hover::after {
      width: 100%;
    }
  }
}

.footer-copyright {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: var(--spacing-4);
  
  .copyright-text {
    font-size: var(--font-size-xs);
    opacity: 0.6;
    margin: 0 0 var(--spacing-1);
  }
  
  .company-info {
    font-size: var(--font-size-xs);
    opacity: 0.8;
    margin: 0;
    font-weight: 500;
  }
}

// 响应式调整
@media (max-width: 480px) {
  .mobile-footer {
    padding: var(--spacing-6) var(--spacing-3) var(--spacing-4);
    
    .footer-links {
      gap: var(--spacing-3) var(--spacing-4);
    }
    
    .back-to-top-btn,
    .more-content-btn {
      padding: var(--spacing-2) var(--spacing-4);
      font-size: var(--font-size-xs);
    }
  }
}

// 动画效果
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.mobile-footer {
  animation: slideInUp 0.6s ease-out;
}
</style>
