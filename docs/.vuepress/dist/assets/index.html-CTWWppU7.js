import{_ as e,c as t,o as a}from"./app-Dc7sG0MX.js";const r={};function c(o,n){return a(),t("div")}const s=e(r,[["render",c]]),i=JSON.parse('{"path":"/tag/wwii/","title":"Tag WWII","lang":"zh-CN","frontmatter":{"title":"Tag WWII","sidebar":false,"blog":{"type":"category","name":"WWII","key":"tag"},"layout":"Tag"},"headers":[],"git":{},"filePathRelative":null,"excerpt":""}');export{s as comp,i as data};
