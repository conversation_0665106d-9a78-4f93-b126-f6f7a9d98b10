import{_ as a,c as t,a as i,o as n}from"./app-Dc7sG0MX.js";const s={};function c(r,e){return n(),t("div",null,e[0]||(e[0]=[i('<h1 id="sticky-article" tabindex="-1"><a class="header-anchor" href="#sticky-article"><span>Sticky Article</span></a></h1><h2 id="heading-2" tabindex="-1"><a class="header-anchor" href="#heading-2"><span>Heading 2</span></a></h2><p>Here is the content.</p><h3 id="heading-3" tabindex="-1"><a class="header-anchor" href="#heading-3"><span>Heading 3</span></a></h3><p>Here is the content.</p>',5)]))}const h=a(s,[["render",c]]),l=JSON.parse('{"path":"/posts/sticky.html","title":"Sticky Article","lang":"zh-CN","frontmatter":{"date":"2021-01-01T00:00:00.000Z","category":["Category C"],"tag":["tag E"],"sticky":true,"excerpt":"<p>A sticky article demo.</p>"},"headers":[{"level":2,"title":"Heading 2","slug":"heading-2","link":"#heading-2","children":[{"level":3,"title":"Heading 3","slug":"heading-3","link":"#heading-3","children":[]}]}],"git":{},"filePathRelative":"posts/sticky.md"}');export{h as comp,l as data};
