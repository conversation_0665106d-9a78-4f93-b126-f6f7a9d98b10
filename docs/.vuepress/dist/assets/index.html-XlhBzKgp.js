import{_ as e,c as t,o as a}from"./app-Dc7sG0MX.js";const r={};function o(c,n){return a(),t("div")}const s=e(r,[["render",o]]),g=JSON.parse('{"path":"/category/category-a/","title":"Category Category A","lang":"zh-CN","frontmatter":{"title":"Category Category A","sidebar":false,"blog":{"type":"category","name":"Category A","key":"category"},"layout":"Category"},"headers":[],"git":{},"filePathRelative":null,"excerpt":""}');export{s as comp,g as data};
