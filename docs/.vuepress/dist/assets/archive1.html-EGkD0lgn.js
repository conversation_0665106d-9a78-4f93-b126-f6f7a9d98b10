import{_ as a,c as n,a as t,o as i}from"./app-Dc7sG0MX.js";const r={};function h(c,e){return i(),n("div",null,e[0]||(e[0]=[t('<h1 id="archive-article1" tabindex="-1"><a class="header-anchor" href="#archive-article1"><span>Archive Article1</span></a></h1><h2 id="heading-2" tabindex="-1"><a class="header-anchor" href="#heading-2"><span>Heading 2</span></a></h2><p>Here is the content.</p><h3 id="heading-3" tabindex="-1"><a class="header-anchor" href="#heading-3"><span>Heading 3</span></a></h3><p>Here is the content.</p>',5)]))}const d=a(r,[["render",h]]),l=JSON.parse('{"path":"/posts/archive1.html","title":"Archive Article1","lang":"zh-CN","frontmatter":{"date":"1998-01-01T00:00:00.000Z","category":["History"],"tag":["WWI"],"archive":true},"headers":[{"level":2,"title":"Heading 2","slug":"heading-2","link":"#heading-2","children":[{"level":3,"title":"Heading 3","slug":"heading-3","link":"#heading-3","children":[]}]}],"git":{},"filePathRelative":"posts/archive1.md","excerpt":"\\n<h2>Heading 2</h2>\\n<p>Here is the content.</p>\\n<h3>Heading 3</h3>\\n<p>Here is the content.</p>\\n"}');export{d as comp,l as data};
