import{_ as i,c as t,a,o as n}from"./app-Dc7sG0MX.js";const r={};function h(c,e){return n(),t("div",null,e[0]||(e[0]=[a('<h1 id="sticky-article-with-higher-priority" tabindex="-1"><a class="header-anchor" href="#sticky-article-with-higher-priority"><span>Sticky Article with Higher Priority</span></a></h1><p>Excerpt information which is added manually.</p><h2 id="heading-2" tabindex="-1"><a class="header-anchor" href="#heading-2"><span>Heading 2</span></a></h2><p>Here is the content.</p><h3 id="heading-3" tabindex="-1"><a class="header-anchor" href="#heading-3"><span>Heading 3</span></a></h3><p>Here is the content.</p>',6)]))}const d=i(r,[["render",h]]),l=JSON.parse('{"path":"/posts/sticky2.html","title":"Sticky Article with Higher Priority","lang":"zh-CN","frontmatter":{"date":"2020-01-01T00:00:00.000Z","category":["Category C"],"tag":["tag E"],"sticky":10},"headers":[{"level":2,"title":"Heading 2","slug":"heading-2","link":"#heading-2","children":[{"level":3,"title":"Heading 3","slug":"heading-3","link":"#heading-3","children":[]}]}],"git":{},"filePathRelative":"posts/sticky2.md","excerpt":"\\n<p>Excerpt information which is added manually.</p>\\n"}');export{d as comp,l as data};
