import{_ as s,c as a,a as e,o as l}from"./app-Dc7sG0MX.js";const i={};function t(p,n){return l(),a("div",null,n[0]||(n[0]=[e(`<h1 id="双马智能科技移动端模板系统使用指南" tabindex="-1"><a class="header-anchor" href="#双马智能科技移动端模板系统使用指南"><span>双马智能科技移动端模板系统使用指南</span></a></h1><p>本文档详细介绍了基于VuePress 2.x构建的移动端优先模板系统的使用方法。</p><h2 id="概述" tabindex="-1"><a class="header-anchor" href="#概述"><span>概述</span></a></h2><p>该模板系统专为双马智能科技官网设计，采用移动端优先的响应式设计理念，提供了完整的页面模板和组件库。</p><h3 id="特性" tabindex="-1"><a class="header-anchor" href="#特性"><span>特性</span></a></h3><ul><li>🚀 移动端优先的响应式设计</li><li>🎨 基于设计稿的精确还原</li><li>📱 触摸友好的交互体验</li><li>🔧 模块化组件架构</li><li>⚡ 优化的性能表现</li><li>🎯 SEO友好</li></ul><h2 id="页面布局模板" tabindex="-1"><a class="header-anchor" href="#页面布局模板"><span>页面布局模板</span></a></h2><h3 id="_1-home-布局" tabindex="-1"><a class="header-anchor" href="#_1-home-布局"><span>1. Home 布局</span></a></h3><p>用于首页展示，包含品牌展示和导航卡片。</p><div class="language-markdown line-numbers-mode" data-highlighter="prismjs" data-ext="md"><pre><code><span class="line"><span class="token front-matter-block"><span class="token punctuation">---</span></span>
<span class="line"><span class="token front-matter yaml language-yaml"><span class="token key atrule">layout</span><span class="token punctuation">:</span> Home</span>
<span class="line"><span class="token key atrule">title</span><span class="token punctuation">:</span> 双马智能科技</span>
<span class="line"><span class="token key atrule">description</span><span class="token punctuation">:</span> 电动车仪表专业生产制造商</span></span>
<span class="line"><span class="token punctuation">---</span></span></span>
<span class="line"></span></code></pre><div class="line-numbers" aria-hidden="true" style="counter-reset:line-number 0;"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><p><strong>特性：</strong></p><ul><li>全屏英雄区域</li><li>品牌Logo和标题展示</li><li>产品图片展示</li><li>导航卡片网格</li><li>装饰性背景动画</li></ul><h3 id="_2-companyprofile-布局" tabindex="-1"><a class="header-anchor" href="#_2-companyprofile-布局"><span>2. CompanyProfile 布局</span></a></h3><p>用于公司简介页面，支持图文混排。</p><div class="language-markdown line-numbers-mode" data-highlighter="prismjs" data-ext="md"><pre><code><span class="line"><span class="token front-matter-block"><span class="token punctuation">---</span></span>
<span class="line"><span class="token front-matter yaml language-yaml"><span class="token key atrule">layout</span><span class="token punctuation">:</span> CompanyProfile</span>
<span class="line"><span class="token key atrule">title</span><span class="token punctuation">:</span> 公司简介/Company Profile</span>
<span class="line"><span class="token key atrule">description</span><span class="token punctuation">:</span> 无锡市双马智能科技有限公司</span></span>
<span class="line"><span class="token punctuation">---</span></span></span>
<span class="line"></span></code></pre><div class="line-numbers" aria-hidden="true" style="counter-reset:line-number 0;"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><p><strong>特性：</strong></p><ul><li>移动端导航头部</li><li>页面标题区域</li><li>内容卡片布局</li><li>中英文内容支持</li><li>响应式图片展示</li></ul><h3 id="_3-productdisplay-布局" tabindex="-1"><a class="header-anchor" href="#_3-productdisplay-布局"><span>3. ProductDisplay 布局</span></a></h3><p>用于产品展示页面，支持分类导航。</p><div class="language-markdown line-numbers-mode" data-highlighter="prismjs" data-ext="md"><pre><code><span class="line"><span class="token front-matter-block"><span class="token punctuation">---</span></span>
<span class="line"><span class="token front-matter yaml language-yaml"><span class="token key atrule">layout</span><span class="token punctuation">:</span> ProductDisplay</span>
<span class="line"><span class="token key atrule">title</span><span class="token punctuation">:</span> 产品展示</span>
<span class="line"><span class="token key atrule">description</span><span class="token punctuation">:</span> 双马智能科技产品系列展示</span></span>
<span class="line"><span class="token punctuation">---</span></span></span>
<span class="line"></span></code></pre><div class="line-numbers" aria-hidden="true" style="counter-reset:line-number 0;"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><p><strong>特性：</strong></p><ul><li>产品英雄区域</li><li>分类导航卡片</li><li>渐变色彩搭配</li><li>悬停动画效果</li></ul><h2 id="核心组件" tabindex="-1"><a class="header-anchor" href="#核心组件"><span>核心组件</span></a></h2><h3 id="mobileheader-组件" tabindex="-1"><a class="header-anchor" href="#mobileheader-组件"><span>MobileHeader 组件</span></a></h3><p>移动端页面头部组件，提供导航功能。</p><div class="language-vue line-numbers-mode" data-highlighter="prismjs" data-ext="vue"><pre><code><span class="line"><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>MobileHeader</span> </span>
<span class="line">  <span class="token attr-name">title</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&quot;</span>页面标题<span class="token punctuation">&quot;</span></span></span>
<span class="line">  <span class="token attr-name">:show-back</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&quot;</span>true<span class="token punctuation">&quot;</span></span></span>
<span class="line">  <span class="token attr-name">back-url</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&quot;</span>/<span class="token punctuation">&quot;</span></span></span>
<span class="line">  <span class="token attr-name">:show-logo</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&quot;</span>false<span class="token punctuation">&quot;</span></span></span>
<span class="line">  <span class="token attr-name">:show-home</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&quot;</span>true<span class="token punctuation">&quot;</span></span></span>
<span class="line"><span class="token punctuation">/&gt;</span></span></span>
<span class="line"></span></code></pre><div class="line-numbers" aria-hidden="true" style="counter-reset:line-number 0;"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><p><strong>Props：</strong></p><ul><li><code>title</code>: 页面标题</li><li><code>showBack</code>: 是否显示返回按钮</li><li><code>backUrl</code>: 返回链接</li><li><code>showLogo</code>: 是否显示Logo</li><li><code>showHome</code>: 是否显示首页按钮</li></ul><h3 id="navigationcard-组件" tabindex="-1"><a class="header-anchor" href="#navigationcard-组件"><span>NavigationCard 组件</span></a></h3><p>导航卡片组件，用于创建可点击的导航项。</p><div class="language-vue line-numbers-mode" data-highlighter="prismjs" data-ext="vue"><pre><code><span class="line"><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>NavigationCard</span></span>
<span class="line">  <span class="token attr-name">title</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&quot;</span>卡片标题<span class="token punctuation">&quot;</span></span></span>
<span class="line">  <span class="token attr-name">subtitle</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&quot;</span>副标题<span class="token punctuation">&quot;</span></span></span>
<span class="line">  <span class="token attr-name">icon-text</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&quot;</span>🏢<span class="token punctuation">&quot;</span></span></span>
<span class="line">  <span class="token attr-name">to</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&quot;</span>/company/<span class="token punctuation">&quot;</span></span></span>
<span class="line">  <span class="token attr-name">:show-arrow</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&quot;</span>true<span class="token punctuation">&quot;</span></span></span>
<span class="line"><span class="token punctuation">/&gt;</span></span></span>
<span class="line"></span></code></pre><div class="line-numbers" aria-hidden="true" style="counter-reset:line-number 0;"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><p><strong>Props：</strong></p><ul><li><code>title</code>: 卡片标题</li><li><code>subtitle</code>: 副标题</li><li><code>iconText</code>: 图标文本（emoji）</li><li><code>iconBgColor</code>: 图标背景色</li><li><code>to</code>: 路由链接</li><li><code>size</code>: 尺寸（small/normal/large）</li><li><code>variant</code>: 样式变体（default/primary/secondary）</li></ul><h3 id="mobilefooter-组件" tabindex="-1"><a class="header-anchor" href="#mobilefooter-组件"><span>MobileFooter 组件</span></a></h3><p>移动端页脚组件，提供返回顶部和快速链接。</p><div class="language-vue line-numbers-mode" data-highlighter="prismjs" data-ext="vue"><pre><code><span class="line"><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>MobileFooter</span> </span>
<span class="line">  <span class="token attr-name">:show-back-to-top</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&quot;</span>true<span class="token punctuation">&quot;</span></span></span>
<span class="line">  <span class="token attr-name">:quick-links</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&quot;</span>footerLinks<span class="token punctuation">&quot;</span></span></span>
<span class="line">  <span class="token attr-name">copyright-text</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&quot;</span>© 2022-2023 SPINRED 版权所有<span class="token punctuation">&quot;</span></span></span>
<span class="line"><span class="token punctuation">/&gt;</span></span></span>
<span class="line"></span></code></pre><div class="line-numbers" aria-hidden="true" style="counter-reset:line-number 0;"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><p><strong>Props：</strong></p><ul><li><code>showBackToTop</code>: 是否显示返回顶部</li><li><code>quickLinks</code>: 快速链接数组</li><li><code>copyrightText</code>: 版权文本</li></ul><h2 id="样式系统" tabindex="-1"><a class="header-anchor" href="#样式系统"><span>样式系统</span></a></h2><h3 id="css变量" tabindex="-1"><a class="header-anchor" href="#css变量"><span>CSS变量</span></a></h3><p>系统使用CSS变量定义设计令牌：</p><div class="language-scss line-numbers-mode" data-highlighter="prismjs" data-ext="scss"><pre><code><span class="line"><span class="token selector">:root </span><span class="token punctuation">{</span></span>
<span class="line">  <span class="token property">--brand-primary</span><span class="token punctuation">:</span> #e53e3e<span class="token punctuation">;</span></span>
<span class="line">  <span class="token property">--brand-secondary</span><span class="token punctuation">:</span> #000000<span class="token punctuation">;</span></span>
<span class="line">  <span class="token property">--font-size-base</span><span class="token punctuation">:</span> 1rem<span class="token punctuation">;</span></span>
<span class="line">  <span class="token property">--spacing-4</span><span class="token punctuation">:</span> 1rem<span class="token punctuation">;</span></span>
<span class="line">  <span class="token property">--radius-lg</span><span class="token punctuation">:</span> 0.75rem<span class="token punctuation">;</span></span>
<span class="line"><span class="token punctuation">}</span></span>
<span class="line"></span></code></pre><div class="line-numbers" aria-hidden="true" style="counter-reset:line-number 0;"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><h3 id="响应式断点" tabindex="-1"><a class="header-anchor" href="#响应式断点"><span>响应式断点</span></a></h3><div class="language-scss line-numbers-mode" data-highlighter="prismjs" data-ext="scss"><pre><code><span class="line"><span class="token property"><span class="token variable">$breakpoints</span></span><span class="token punctuation">:</span> <span class="token punctuation">(</span></span>
<span class="line">  <span class="token string">&#39;xs&#39;</span><span class="token punctuation">:</span> 0<span class="token punctuation">,</span></span>
<span class="line">  <span class="token string">&#39;sm&#39;</span><span class="token punctuation">:</span> 576px<span class="token punctuation">,</span></span>
<span class="line">  <span class="token string">&#39;md&#39;</span><span class="token punctuation">:</span> 768px<span class="token punctuation">,</span></span>
<span class="line">  <span class="token string">&#39;lg&#39;</span><span class="token punctuation">:</span> 992px<span class="token punctuation">,</span></span>
<span class="line">  <span class="token string">&#39;xl&#39;</span><span class="token punctuation">:</span> 1200px</span>
<span class="line"><span class="token punctuation">)</span><span class="token punctuation">;</span></span>
<span class="line"></span></code></pre><div class="line-numbers" aria-hidden="true" style="counter-reset:line-number 0;"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><h3 id="工具类" tabindex="-1"><a class="header-anchor" href="#工具类"><span>工具类</span></a></h3><div class="language-scss line-numbers-mode" data-highlighter="prismjs" data-ext="scss"><pre><code><span class="line">.mobile-container  <span class="token comment">// 容器类</span></span>
<span class="line">.mobile-section    <span class="token comment">// 区域类</span></span>
<span class="line">.mobile-grid       <span class="token comment">// 网格布局</span></span>
<span class="line">.btn              <span class="token comment">// 按钮基础类</span></span>
<span class="line">.card             <span class="token comment">// 卡片基础类</span></span>
<span class="line"></span></code></pre><div class="line-numbers" aria-hidden="true" style="counter-reset:line-number 0;"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><h2 id="创建新页面" tabindex="-1"><a class="header-anchor" href="#创建新页面"><span>创建新页面</span></a></h2><h3 id="_1-使用现有布局" tabindex="-1"><a class="header-anchor" href="#_1-使用现有布局"><span>1. 使用现有布局</span></a></h3><div class="language-markdown line-numbers-mode" data-highlighter="prismjs" data-ext="md"><pre><code><span class="line"><span class="token front-matter-block"><span class="token punctuation">---</span></span>
<span class="line"><span class="token front-matter yaml language-yaml"><span class="token key atrule">layout</span><span class="token punctuation">:</span> CompanyProfile</span>
<span class="line"><span class="token key atrule">title</span><span class="token punctuation">:</span> 新页面标题</span>
<span class="line"><span class="token key atrule">description</span><span class="token punctuation">:</span> 页面描述</span></span>
<span class="line"><span class="token punctuation">---</span></span></span>
<span class="line"></span>
<span class="line"><span class="token title important"><span class="token punctuation">#</span> 页面内容</span></span>
<span class="line"></span>
<span class="line">这里是页面的具体内容...</span>
<span class="line"></span></code></pre><div class="line-numbers" aria-hidden="true" style="counter-reset:line-number 0;"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><h3 id="_2-自定义布局" tabindex="-1"><a class="header-anchor" href="#_2-自定义布局"><span>2. 自定义布局</span></a></h3><ol><li>在 <code>docs/.vuepress/layouts/</code> 创建新布局文件</li><li>在 <code>docs/.vuepress/client.js</code> 注册布局</li><li>在页面frontmatter中指定布局</li></ol><h2 id="最佳实践" tabindex="-1"><a class="header-anchor" href="#最佳实践"><span>最佳实践</span></a></h2><h3 id="_1-移动端优先" tabindex="-1"><a class="header-anchor" href="#_1-移动端优先"><span>1. 移动端优先</span></a></h3><ul><li>优先考虑移动端体验</li><li>使用触摸友好的交互元素</li><li>确保文字大小适合移动端阅读</li></ul><h3 id="_2-性能优化" tabindex="-1"><a class="header-anchor" href="#_2-性能优化"><span>2. 性能优化</span></a></h3><ul><li>优化图片大小和格式</li><li>使用懒加载</li><li>减少不必要的动画</li></ul><h3 id="_3-可访问性" tabindex="-1"><a class="header-anchor" href="#_3-可访问性"><span>3. 可访问性</span></a></h3><ul><li>提供适当的alt文本</li><li>确保足够的颜色对比度</li><li>支持键盘导航</li></ul><h2 id="文件结构" tabindex="-1"><a class="header-anchor" href="#文件结构"><span>文件结构</span></a></h2><div class="language-text line-numbers-mode" data-highlighter="prismjs" data-ext="text"><pre><code><span class="line">docs/</span>
<span class="line">├── .vuepress/</span>
<span class="line">│   ├── components/          # 组件目录</span>
<span class="line">│   │   ├── MobileHeader.vue</span>
<span class="line">│   │   ├── NavigationCard.vue</span>
<span class="line">│   │   └── MobileFooter.vue</span>
<span class="line">│   ├── layouts/            # 布局目录</span>
<span class="line">│   │   ├── Home.vue</span>
<span class="line">│   │   ├── CompanyProfile.vue</span>
<span class="line">│   │   └── ProductDisplay.vue</span>
<span class="line">│   ├── styles/             # 样式目录</span>
<span class="line">│   │   ├── index.scss</span>
<span class="line">│   │   └── mobile.scss</span>
<span class="line">│   ├── config.js           # 配置文件</span>
<span class="line">│   └── client.js           # 客户端配置</span>
<span class="line">├── company/                # 公司简介</span>
<span class="line">├── products/               # 产品展示</span>
<span class="line">└── README.md               # 首页</span>
<span class="line"></span></code></pre><div class="line-numbers" aria-hidden="true" style="counter-reset:line-number 0;"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><h2 id="部署说明" tabindex="-1"><a class="header-anchor" href="#部署说明"><span>部署说明</span></a></h2><ol><li>构建项目：<code>npm run docs:build</code></li><li>部署dist目录到服务器</li><li>确保服务器支持SPA路由</li></ol><h2 id="技术支持" tabindex="-1"><a class="header-anchor" href="#技术支持"><span>技术支持</span></a></h2><p>如有问题，请参考：</p><ul><li><a href="https://v2.vuepress.vuejs.org/" target="_blank" rel="noopener noreferrer">VuePress 2.x 官方文档</a></li><li><a href="https://vuejs.org/" target="_blank" rel="noopener noreferrer">Vue 3 官方文档</a></li><li><a href="https://sass-lang.com/" target="_blank" rel="noopener noreferrer">SCSS 文档</a></li></ul>`,65)]))}const o=s(i,[["render",t]]),r=JSON.parse('{"path":"/template-guide.html","title":"双马智能科技移动端模板系统使用指南","lang":"zh-CN","frontmatter":{},"headers":[{"level":2,"title":"概述","slug":"概述","link":"#概述","children":[{"level":3,"title":"特性","slug":"特性","link":"#特性","children":[]}]},{"level":2,"title":"页面布局模板","slug":"页面布局模板","link":"#页面布局模板","children":[{"level":3,"title":"1. Home 布局","slug":"_1-home-布局","link":"#_1-home-布局","children":[]},{"level":3,"title":"2. CompanyProfile 布局","slug":"_2-companyprofile-布局","link":"#_2-companyprofile-布局","children":[]},{"level":3,"title":"3. ProductDisplay 布局","slug":"_3-productdisplay-布局","link":"#_3-productdisplay-布局","children":[]}]},{"level":2,"title":"核心组件","slug":"核心组件","link":"#核心组件","children":[{"level":3,"title":"MobileHeader 组件","slug":"mobileheader-组件","link":"#mobileheader-组件","children":[]},{"level":3,"title":"NavigationCard 组件","slug":"navigationcard-组件","link":"#navigationcard-组件","children":[]},{"level":3,"title":"MobileFooter 组件","slug":"mobilefooter-组件","link":"#mobilefooter-组件","children":[]}]},{"level":2,"title":"样式系统","slug":"样式系统","link":"#样式系统","children":[{"level":3,"title":"CSS变量","slug":"css变量","link":"#css变量","children":[]},{"level":3,"title":"响应式断点","slug":"响应式断点","link":"#响应式断点","children":[]},{"level":3,"title":"工具类","slug":"工具类","link":"#工具类","children":[]}]},{"level":2,"title":"创建新页面","slug":"创建新页面","link":"#创建新页面","children":[{"level":3,"title":"1. 使用现有布局","slug":"_1-使用现有布局","link":"#_1-使用现有布局","children":[]},{"level":3,"title":"2. 自定义布局","slug":"_2-自定义布局","link":"#_2-自定义布局","children":[]}]},{"level":2,"title":"最佳实践","slug":"最佳实践","link":"#最佳实践","children":[{"level":3,"title":"1. 移动端优先","slug":"_1-移动端优先","link":"#_1-移动端优先","children":[]},{"level":3,"title":"2. 性能优化","slug":"_2-性能优化","link":"#_2-性能优化","children":[]},{"level":3,"title":"3. 可访问性","slug":"_3-可访问性","link":"#_3-可访问性","children":[]}]},{"level":2,"title":"文件结构","slug":"文件结构","link":"#文件结构","children":[]},{"level":2,"title":"部署说明","slug":"部署说明","link":"#部署说明","children":[]},{"level":2,"title":"技术支持","slug":"技术支持","link":"#技术支持","children":[]}],"git":{},"filePathRelative":"template-guide.md","excerpt":"\\n<p>本文档详细介绍了基于VuePress 2.x构建的移动端优先模板系统的使用方法。</p>\\n<h2>概述</h2>\\n<p>该模板系统专为双马智能科技官网设计，采用移动端优先的响应式设计理念，提供了完整的页面模板和组件库。</p>\\n<h3>特性</h3>\\n<ul>\\n<li>🚀 移动端优先的响应式设计</li>\\n<li>🎨 基于设计稿的精确还原</li>\\n<li>📱 触摸友好的交互体验</li>\\n<li>🔧 模块化组件架构</li>\\n<li>⚡ 优化的性能表现</li>\\n<li>🎯 SEO友好</li>\\n</ul>\\n<h2>页面布局模板</h2>\\n<h3>1. Home 布局</h3>"}');export{o as comp,r as data};
