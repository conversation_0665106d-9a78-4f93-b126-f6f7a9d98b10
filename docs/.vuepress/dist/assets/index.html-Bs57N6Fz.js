import{_ as t,c as e,o as a}from"./app-Dc7sG0MX.js";const c={};function r(o,n){return a(),e("div")}const s=t(c,[["render",r]]),g=JSON.parse('{"path":"/tag/tag-c/","title":"Tag tag C","lang":"zh-CN","frontmatter":{"title":"Tag tag C","sidebar":false,"blog":{"type":"category","name":"tag C","key":"tag"},"layout":"Tag"},"headers":[],"git":{},"filePathRelative":null,"excerpt":""}');export{s as comp,g as data};
