import{_ as t,c as e,o as a}from"./app-Dc7sG0MX.js";const r={};function c(o,n){return a(),e("div")}const s=t(r,[["render",c]]),g=JSON.parse('{"path":"/tag/tag-e/","title":"Tag tag E","lang":"zh-CN","frontmatter":{"title":"Tag tag E","sidebar":false,"blog":{"type":"category","name":"tag E","key":"tag"},"layout":"Tag"},"headers":[],"git":{},"filePathRelative":null,"excerpt":""}');export{s as comp,g as data};
