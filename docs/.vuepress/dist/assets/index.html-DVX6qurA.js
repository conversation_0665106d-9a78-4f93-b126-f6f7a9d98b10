import{_ as t,c as e,o as r}from"./app-Dc7sG0MX.js";const a={};function o(c,s){return r(),e("div")}const i=t(a,[["render",o]]),l=JSON.parse('{"path":"/category/history/","title":"Category History","lang":"zh-CN","frontmatter":{"title":"Category History","sidebar":false,"blog":{"type":"category","name":"History","key":"category"},"layout":"Category"},"headers":[],"git":{},"filePathRelative":null,"excerpt":""}');export{i as comp,l as data};
