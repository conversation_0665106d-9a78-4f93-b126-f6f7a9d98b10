/**
* @vue/shared v3.5.17
* (c) 2018-present <PERSON><PERSON> (<PERSON>) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function rs(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const Se={},bn=[],St=()=>{},Cc=()=>!1,ir=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),os=e=>e.startsWith("onUpdate:"),Me=Object.assign,ss=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},Tc=Object.prototype.hasOwnProperty,ye=(e,t)=>Tc.call(e,t),se=Array.isArray,En=e=>Jr(e)==="[object Map]",sa=e=>Jr(e)==="[object Set]",ie=e=>typeof e=="function",ke=e=>typeof e=="string",Dt=e=>typeof e=="symbol",xe=e=>e!==null&&typeof e=="object",ia=e=>(xe(e)||ie(e))&&ie(e.then)&&ie(e.catch),aa=Object.prototype.toString,Jr=e=>aa.call(e),xc=e=>Jr(e).slice(8,-1),la=e=>Jr(e)==="[object Object]",is=e=>ke(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,wn=rs(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),Yr=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Ac=/-(\w)/g,Qe=Yr(e=>e.replace(Ac,(t,n)=>n?n.toUpperCase():"")),kc=/\B([A-Z])/g,Jt=Yr(e=>e.replace(kc,"-$1").toLowerCase()),ar=Yr(e=>e.charAt(0).toUpperCase()+e.slice(1)),go=Yr(e=>e?`on${ar(e)}`:""),qt=(e,t)=>!Object.is(e,t),mo=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},No=(e,t,n,r=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:r,value:n})},Lc=e=>{const t=parseFloat(e);return isNaN(t)?e:t},Pc=e=>{const t=ke(e)?Number(e):NaN;return isNaN(t)?e:t};let Bs;const Qr=()=>Bs||(Bs=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Yt(e){if(se(e)){const t={};for(let n=0;n<e.length;n++){const r=e[n],o=ke(r)?Ic(r):Yt(r);if(o)for(const s in o)t[s]=o[s]}return t}else if(ke(e)||xe(e))return e}const Rc=/;(?![^(]*\))/g,$c=/:([^]+)/,Oc=/\/\*[^]*?\*\//g;function Ic(e){const t={};return e.replace(Oc,"").split(Rc).forEach(n=>{if(n){const r=n.split($c);r.length>1&&(t[r[0].trim()]=r[1].trim())}}),t}function je(e){let t="";if(ke(e))t=e;else if(se(e))for(let n=0;n<e.length;n++){const r=je(e[n]);r&&(t+=r+" ")}else if(xe(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}function vo(e){if(!e)return null;let{class:t,style:n}=e;return t&&!ke(t)&&(e.class=je(t)),n&&(e.style=Yt(n)),e}const Hc="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",Mc=rs(Hc);function ca(e){return!!e||e===""}const ua=e=>!!(e&&e.__v_isRef===!0),le=e=>ke(e)?e:e==null?"":se(e)||xe(e)&&(e.toString===aa||!ie(e.toString))?ua(e)?le(e.value):JSON.stringify(e,fa,2):String(e),fa=(e,t)=>ua(t)?fa(e,t.value):En(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[r,o],s)=>(n[_o(r,s)+" =>"]=o,n),{})}:sa(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>_o(n))}:Dt(t)?_o(t):xe(t)&&!se(t)&&!la(t)?String(t):t,_o=(e,t="")=>{var n;return Dt(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Ue;class Dc{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=Ue,!t&&Ue&&(this.index=(Ue.scopes||(Ue.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=Ue;try{return Ue=this,t()}finally{Ue=n}}}on(){++this._on===1&&(this.prevScope=Ue,Ue=this)}off(){this._on>0&&--this._on===0&&(Ue=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let n,r;for(n=0,r=this.effects.length;n<r;n++)this.effects[n].stop();for(this.effects.length=0,n=0,r=this.cleanups.length;n<r;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,r=this.scopes.length;n<r;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const o=this.parent.scopes.pop();o&&o!==this&&(this.parent.scopes[this.index]=o,o.index=this.index)}this.parent=void 0}}}function da(){return Ue}function Nc(e,t=!1){Ue&&Ue.cleanups.push(e)}let Te;const yo=new WeakSet;class ha{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,Ue&&Ue.active&&Ue.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,yo.has(this)&&(yo.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||ga(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,js(this),ma(this);const t=Te,n=ht;Te=this,ht=!0;try{return this.fn()}finally{va(this),Te=t,ht=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)cs(t);this.deps=this.depsTail=void 0,js(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?yo.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){Bo(this)&&this.run()}get dirty(){return Bo(this)}}let pa=0,zn,Un;function ga(e,t=!1){if(e.flags|=8,t){e.next=Un,Un=e;return}e.next=zn,zn=e}function as(){pa++}function ls(){if(--pa>0)return;if(Un){let t=Un;for(Un=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;zn;){let t=zn;for(zn=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(r){e||(e=r)}t=n}}if(e)throw e}function ma(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function va(e){let t,n=e.depsTail,r=n;for(;r;){const o=r.prevDep;r.version===-1?(r===n&&(n=o),cs(r),Bc(r)):t=r,r.dep.activeLink=r.prevActiveLink,r.prevActiveLink=void 0,r=o}e.deps=t,e.depsTail=n}function Bo(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(_a(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function _a(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===Jn)||(e.globalVersion=Jn,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!Bo(e))))return;e.flags|=2;const t=e.dep,n=Te,r=ht;Te=e,ht=!0;try{ma(e);const o=e.fn(e._value);(t.version===0||qt(o,e._value))&&(e.flags|=128,e._value=o,t.version++)}catch(o){throw t.version++,o}finally{Te=n,ht=r,va(e),e.flags&=-3}}function cs(e,t=!1){const{dep:n,prevSub:r,nextSub:o}=e;if(r&&(r.nextSub=o,e.prevSub=void 0),o&&(o.prevSub=r,e.nextSub=void 0),n.subs===e&&(n.subs=r,!r&&n.computed)){n.computed.flags&=-5;for(let s=n.computed.deps;s;s=s.nextDep)cs(s,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function Bc(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let ht=!0;const ya=[];function Ht(){ya.push(ht),ht=!1}function Mt(){const e=ya.pop();ht=e===void 0?!0:e}function js(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=Te;Te=void 0;try{t()}finally{Te=n}}}let Jn=0;class jc{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Xr{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.__v_skip=!0}track(t){if(!Te||!ht||Te===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==Te)n=this.activeLink=new jc(Te,this),Te.deps?(n.prevDep=Te.depsTail,Te.depsTail.nextDep=n,Te.depsTail=n):Te.deps=Te.depsTail=n,ba(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const r=n.nextDep;r.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=r),n.prevDep=Te.depsTail,n.nextDep=void 0,Te.depsTail.nextDep=n,Te.depsTail=n,Te.deps===n&&(Te.deps=r)}return n}trigger(t){this.version++,Jn++,this.notify(t)}notify(t){as();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{ls()}}}function ba(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let r=t.deps;r;r=r.nextDep)ba(r)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const Nr=new WeakMap,an=Symbol(""),jo=Symbol(""),Yn=Symbol("");function We(e,t,n){if(ht&&Te){let r=Nr.get(e);r||Nr.set(e,r=new Map);let o=r.get(n);o||(r.set(n,o=new Xr),o.map=r,o.key=n),o.track()}}function $t(e,t,n,r,o,s){const i=Nr.get(e);if(!i){Jn++;return}const a=l=>{l&&l.trigger()};if(as(),t==="clear")i.forEach(a);else{const l=se(e),c=l&&is(n);if(l&&n==="length"){const u=Number(r);i.forEach((f,p)=>{(p==="length"||p===Yn||!Dt(p)&&p>=u)&&a(f)})}else switch((n!==void 0||i.has(void 0))&&a(i.get(n)),c&&a(i.get(Yn)),t){case"add":l?c&&a(i.get("length")):(a(i.get(an)),En(e)&&a(i.get(jo)));break;case"delete":l||(a(i.get(an)),En(e)&&a(i.get(jo)));break;case"set":En(e)&&a(i.get(an));break}}ls()}function Fc(e,t){const n=Nr.get(e);return n&&n.get(t)}function pn(e){const t=he(e);return t===e?t:(We(t,"iterate",Yn),ct(e)?t:t.map(Ne))}function eo(e){return We(e=he(e),"iterate",Yn),e}const Vc={__proto__:null,[Symbol.iterator](){return bo(this,Symbol.iterator,Ne)},concat(...e){return pn(this).concat(...e.map(t=>se(t)?pn(t):t))},entries(){return bo(this,"entries",e=>(e[1]=Ne(e[1]),e))},every(e,t){return xt(this,"every",e,t,void 0,arguments)},filter(e,t){return xt(this,"filter",e,t,n=>n.map(Ne),arguments)},find(e,t){return xt(this,"find",e,t,Ne,arguments)},findIndex(e,t){return xt(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return xt(this,"findLast",e,t,Ne,arguments)},findLastIndex(e,t){return xt(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return xt(this,"forEach",e,t,void 0,arguments)},includes(...e){return Eo(this,"includes",e)},indexOf(...e){return Eo(this,"indexOf",e)},join(e){return pn(this).join(e)},lastIndexOf(...e){return Eo(this,"lastIndexOf",e)},map(e,t){return xt(this,"map",e,t,void 0,arguments)},pop(){return Mn(this,"pop")},push(...e){return Mn(this,"push",e)},reduce(e,...t){return Fs(this,"reduce",e,t)},reduceRight(e,...t){return Fs(this,"reduceRight",e,t)},shift(){return Mn(this,"shift")},some(e,t){return xt(this,"some",e,t,void 0,arguments)},splice(...e){return Mn(this,"splice",e)},toReversed(){return pn(this).toReversed()},toSorted(e){return pn(this).toSorted(e)},toSpliced(...e){return pn(this).toSpliced(...e)},unshift(...e){return Mn(this,"unshift",e)},values(){return bo(this,"values",Ne)}};function bo(e,t,n){const r=eo(e),o=r[t]();return r!==e&&!ct(e)&&(o._next=o.next,o.next=()=>{const s=o._next();return s.value&&(s.value=n(s.value)),s}),o}const zc=Array.prototype;function xt(e,t,n,r,o,s){const i=eo(e),a=i!==e&&!ct(e),l=i[t];if(l!==zc[t]){const f=l.apply(e,s);return a?Ne(f):f}let c=n;i!==e&&(a?c=function(f,p){return n.call(this,Ne(f),p,e)}:n.length>2&&(c=function(f,p){return n.call(this,f,p,e)}));const u=l.call(i,c,r);return a&&o?o(u):u}function Fs(e,t,n,r){const o=eo(e);let s=n;return o!==e&&(ct(e)?n.length>3&&(s=function(i,a,l){return n.call(this,i,a,l,e)}):s=function(i,a,l){return n.call(this,i,Ne(a),l,e)}),o[t](s,...r)}function Eo(e,t,n){const r=he(e);We(r,"iterate",Yn);const o=r[t](...n);return(o===-1||o===!1)&&us(n[0])?(n[0]=he(n[0]),r[t](...n)):o}function Mn(e,t,n=[]){Ht(),as();const r=he(e)[t].apply(e,n);return ls(),Mt(),r}const Uc=rs("__proto__,__v_isRef,__isVue"),Ea=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Dt));function Wc(e){Dt(e)||(e=String(e));const t=he(this);return We(t,"has",e),t.hasOwnProperty(e)}class wa{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,r){if(n==="__v_skip")return t.__v_skip;const o=this._isReadonly,s=this._isShallow;if(n==="__v_isReactive")return!o;if(n==="__v_isReadonly")return o;if(n==="__v_isShallow")return s;if(n==="__v_raw")return r===(o?s?ka:Aa:s?xa:Ta).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(r)?t:void 0;const i=se(t);if(!o){let l;if(i&&(l=Vc[n]))return l;if(n==="hasOwnProperty")return Wc}const a=Reflect.get(t,n,Oe(t)?t:r);return(Dt(n)?Ea.has(n):Uc(n))||(o||We(t,"get",n),s)?a:Oe(a)?i&&is(n)?a:a.value:xe(a)?o?$n(a):lr(a):a}}class Sa extends wa{constructor(t=!1){super(!1,t)}set(t,n,r,o){let s=t[n];if(!this._isShallow){const l=Gt(s);if(!ct(r)&&!Gt(r)&&(s=he(s),r=he(r)),!se(t)&&Oe(s)&&!Oe(r))return l?!1:(s.value=r,!0)}const i=se(t)&&is(n)?Number(n)<t.length:ye(t,n),a=Reflect.set(t,n,r,Oe(t)?t:o);return t===he(o)&&(i?qt(r,s)&&$t(t,"set",n,r):$t(t,"add",n,r)),a}deleteProperty(t,n){const r=ye(t,n);t[n];const o=Reflect.deleteProperty(t,n);return o&&r&&$t(t,"delete",n,void 0),o}has(t,n){const r=Reflect.has(t,n);return(!Dt(n)||!Ea.has(n))&&We(t,"has",n),r}ownKeys(t){return We(t,"iterate",se(t)?"length":an),Reflect.ownKeys(t)}}class Ca extends wa{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const qc=new Sa,Kc=new Ca,Gc=new Sa(!0),Zc=new Ca(!0),Fo=e=>e,br=e=>Reflect.getPrototypeOf(e);function Jc(e,t,n){return function(...r){const o=this.__v_raw,s=he(o),i=En(s),a=e==="entries"||e===Symbol.iterator&&i,l=e==="keys"&&i,c=o[e](...r),u=n?Fo:t?Br:Ne;return!t&&We(s,"iterate",l?jo:an),{next(){const{value:f,done:p}=c.next();return p?{value:f,done:p}:{value:a?[u(f[0]),u(f[1])]:u(f),done:p}},[Symbol.iterator](){return this}}}}function Er(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function Yc(e,t){const n={get(o){const s=this.__v_raw,i=he(s),a=he(o);e||(qt(o,a)&&We(i,"get",o),We(i,"get",a));const{has:l}=br(i),c=t?Fo:e?Br:Ne;if(l.call(i,o))return c(s.get(o));if(l.call(i,a))return c(s.get(a));s!==i&&s.get(o)},get size(){const o=this.__v_raw;return!e&&We(he(o),"iterate",an),Reflect.get(o,"size",o)},has(o){const s=this.__v_raw,i=he(s),a=he(o);return e||(qt(o,a)&&We(i,"has",o),We(i,"has",a)),o===a?s.has(o):s.has(o)||s.has(a)},forEach(o,s){const i=this,a=i.__v_raw,l=he(a),c=t?Fo:e?Br:Ne;return!e&&We(l,"iterate",an),a.forEach((u,f)=>o.call(s,c(u),c(f),i))}};return Me(n,e?{add:Er("add"),set:Er("set"),delete:Er("delete"),clear:Er("clear")}:{add(o){!t&&!ct(o)&&!Gt(o)&&(o=he(o));const s=he(this);return br(s).has.call(s,o)||(s.add(o),$t(s,"add",o,o)),this},set(o,s){!t&&!ct(s)&&!Gt(s)&&(s=he(s));const i=he(this),{has:a,get:l}=br(i);let c=a.call(i,o);c||(o=he(o),c=a.call(i,o));const u=l.call(i,o);return i.set(o,s),c?qt(s,u)&&$t(i,"set",o,s):$t(i,"add",o,s),this},delete(o){const s=he(this),{has:i,get:a}=br(s);let l=i.call(s,o);l||(o=he(o),l=i.call(s,o)),a&&a.call(s,o);const c=s.delete(o);return l&&$t(s,"delete",o,void 0),c},clear(){const o=he(this),s=o.size!==0,i=o.clear();return s&&$t(o,"clear",void 0,void 0),i}}),["keys","values","entries",Symbol.iterator].forEach(o=>{n[o]=Jc(o,e,t)}),n}function to(e,t){const n=Yc(e,t);return(r,o,s)=>o==="__v_isReactive"?!e:o==="__v_isReadonly"?e:o==="__v_raw"?r:Reflect.get(ye(n,o)&&o in r?n:r,o,s)}const Qc={get:to(!1,!1)},Xc={get:to(!1,!0)},eu={get:to(!0,!1)},tu={get:to(!0,!0)},Ta=new WeakMap,xa=new WeakMap,Aa=new WeakMap,ka=new WeakMap;function nu(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function ru(e){return e.__v_skip||!Object.isExtensible(e)?0:nu(xc(e))}function lr(e){return Gt(e)?e:no(e,!1,qc,Qc,Ta)}function La(e){return no(e,!1,Gc,Xc,xa)}function $n(e){return no(e,!0,Kc,eu,Aa)}function ou(e){return no(e,!0,Zc,tu,ka)}function no(e,t,n,r,o){if(!xe(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const s=ru(e);if(s===0)return e;const i=o.get(e);if(i)return i;const a=new Proxy(e,s===2?r:n);return o.set(e,a),a}function ln(e){return Gt(e)?ln(e.__v_raw):!!(e&&e.__v_isReactive)}function Gt(e){return!!(e&&e.__v_isReadonly)}function ct(e){return!!(e&&e.__v_isShallow)}function us(e){return e?!!e.__v_raw:!1}function he(e){const t=e&&e.__v_raw;return t?he(t):e}function su(e){return!ye(e,"__v_skip")&&Object.isExtensible(e)&&No(e,"__v_skip",!0),e}const Ne=e=>xe(e)?lr(e):e,Br=e=>xe(e)?$n(e):e;function Oe(e){return e?e.__v_isRef===!0:!1}function qe(e){return Pa(e,!1)}function Pe(e){return Pa(e,!0)}function Pa(e,t){return Oe(e)?e:new iu(e,t)}class iu{constructor(t,n){this.dep=new Xr,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:he(t),this._value=n?t:Ne(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,r=this.__v_isShallow||ct(t)||Gt(t);t=r?t:he(t),qt(t,n)&&(this._rawValue=t,this._value=r?t:Ne(t),this.dep.trigger())}}function B(e){return Oe(e)?e.value:e}function He(e){return ie(e)?e():B(e)}const au={get:(e,t,n)=>t==="__v_raw"?e:B(Reflect.get(e,t,n)),set:(e,t,n,r)=>{const o=e[t];return Oe(o)&&!Oe(n)?(o.value=n,!0):Reflect.set(e,t,n,r)}};function Ra(e){return ln(e)?e:new Proxy(e,au)}class lu{constructor(t){this.__v_isRef=!0,this._value=void 0;const n=this.dep=new Xr,{get:r,set:o}=t(n.track.bind(n),n.trigger.bind(n));this._get=r,this._set=o}get value(){return this._value=this._get()}set value(t){this._set(t)}}function $a(e){return new lu(e)}function Oa(e){const t=se(e)?new Array(e.length):{};for(const n in e)t[n]=Ha(e,n);return t}class cu{constructor(t,n,r){this._object=t,this._key=n,this._defaultValue=r,this.__v_isRef=!0,this._value=void 0}get value(){const t=this._object[this._key];return this._value=t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return Fc(he(this._object),this._key)}}class uu{constructor(t){this._getter=t,this.__v_isRef=!0,this.__v_isReadonly=!0,this._value=void 0}get value(){return this._value=this._getter()}}function Ia(e,t,n){return Oe(e)?e:ie(e)?new uu(e):xe(e)&&arguments.length>1?Ha(e,t,n):qe(e)}function Ha(e,t,n){const r=e[t];return Oe(r)?r:new cu(e,t,n)}class fu{constructor(t,n,r){this.fn=t,this.setter=n,this._value=void 0,this.dep=new Xr(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=Jn-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=r}notify(){if(this.flags|=16,!(this.flags&8)&&Te!==this)return ga(this,!0),!0}get value(){const t=this.dep.track();return _a(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function du(e,t,n=!1){let r,o;return ie(e)?r=e:(r=e.get,o=e.set),new fu(r,o,n)}const wr={},jr=new WeakMap;let rn;function hu(e,t=!1,n=rn){if(n){let r=jr.get(n);r||jr.set(n,r=[]),r.push(e)}}function pu(e,t,n=Se){const{immediate:r,deep:o,once:s,scheduler:i,augmentJob:a,call:l}=n,c=y=>o?y:ct(y)||o===!1||o===0?Ot(y,1):Ot(y);let u,f,p,g,_=!1,b=!1;if(Oe(e)?(f=()=>e.value,_=ct(e)):ln(e)?(f=()=>c(e),_=!0):se(e)?(b=!0,_=e.some(y=>ln(y)||ct(y)),f=()=>e.map(y=>{if(Oe(y))return y.value;if(ln(y))return c(y);if(ie(y))return l?l(y,2):y()})):ie(e)?t?f=l?()=>l(e,2):e:f=()=>{if(p){Ht();try{p()}finally{Mt()}}const y=rn;rn=u;try{return l?l(e,3,[g]):e(g)}finally{rn=y}}:f=St,t&&o){const y=f,N=o===!0?1/0:o;f=()=>Ot(y(),N)}const w=da(),A=()=>{u.stop(),w&&w.active&&ss(w.effects,u)};if(s&&t){const y=t;t=(...N)=>{y(...N),A()}}let k=b?new Array(e.length).fill(wr):wr;const m=y=>{if(!(!(u.flags&1)||!u.dirty&&!y))if(t){const N=u.run();if(o||_||(b?N.some((Y,M)=>qt(Y,k[M])):qt(N,k))){p&&p();const Y=rn;rn=u;try{const M=[N,k===wr?void 0:b&&k[0]===wr?[]:k,g];k=N,l?l(t,3,M):t(...M)}finally{rn=Y}}}else u.run()};return a&&a(m),u=new ha(f),u.scheduler=i?()=>i(m,!1):m,g=y=>hu(y,!1,u),p=u.onStop=()=>{const y=jr.get(u);if(y){if(l)l(y,4);else for(const N of y)N();jr.delete(u)}},t?r?m(!0):k=u.run():i?i(m.bind(null,!0),!0):u.run(),A.pause=u.pause.bind(u),A.resume=u.resume.bind(u),A.stop=A,A}function Ot(e,t=1/0,n){if(t<=0||!xe(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,Oe(e))Ot(e.value,t,n);else if(se(e))for(let r=0;r<e.length;r++)Ot(e[r],t,n);else if(sa(e)||En(e))e.forEach(r=>{Ot(r,t,n)});else if(la(e)){for(const r in e)Ot(e[r],t,n);for(const r of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,r)&&Ot(e[r],t,n)}return e}/**
* @vue/runtime-core v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function cr(e,t,n,r){try{return r?e(...r):e()}catch(o){ur(o,t,n)}}function gt(e,t,n,r){if(ie(e)){const o=cr(e,t,n,r);return o&&ia(o)&&o.catch(s=>{ur(s,t,n)}),o}if(se(e)){const o=[];for(let s=0;s<e.length;s++)o.push(gt(e[s],t,n,r));return o}}function ur(e,t,n,r=!0){const o=t?t.vnode:null,{errorHandler:s,throwUnhandledErrorInProduction:i}=t&&t.appContext.config||Se;if(t){let a=t.parent;const l=t.proxy,c=`https://vuejs.org/error-reference/#runtime-${n}`;for(;a;){const u=a.ec;if(u){for(let f=0;f<u.length;f++)if(u[f](e,l,c)===!1)return}a=a.parent}if(s){Ht(),cr(s,null,10,[e,l,c]),Mt();return}}gu(e,n,o,r,i)}function gu(e,t,n,r=!0,o=!1){if(o)throw e;console.error(e)}const Ge=[];let bt=-1;const Sn=[];let Vt=null,vn=0;const Ma=Promise.resolve();let Fr=null;function fr(e){const t=Fr||Ma;return e?t.then(this?e.bind(this):e):t}function mu(e){let t=bt+1,n=Ge.length;for(;t<n;){const r=t+n>>>1,o=Ge[r],s=Qn(o);s<e||s===e&&o.flags&2?t=r+1:n=r}return t}function fs(e){if(!(e.flags&1)){const t=Qn(e),n=Ge[Ge.length-1];!n||!(e.flags&2)&&t>=Qn(n)?Ge.push(e):Ge.splice(mu(t),0,e),e.flags|=1,Da()}}function Da(){Fr||(Fr=Ma.then(Na))}function vu(e){se(e)?Sn.push(...e):Vt&&e.id===-1?Vt.splice(vn+1,0,e):e.flags&1||(Sn.push(e),e.flags|=1),Da()}function Vs(e,t,n=bt+1){for(;n<Ge.length;n++){const r=Ge[n];if(r&&r.flags&2){if(e&&r.id!==e.uid)continue;Ge.splice(n,1),n--,r.flags&4&&(r.flags&=-2),r(),r.flags&4||(r.flags&=-2)}}}function Vr(e){if(Sn.length){const t=[...new Set(Sn)].sort((n,r)=>Qn(n)-Qn(r));if(Sn.length=0,Vt){Vt.push(...t);return}for(Vt=t,vn=0;vn<Vt.length;vn++){const n=Vt[vn];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}Vt=null,vn=0}}const Qn=e=>e.id==null?e.flags&2?-1:1/0:e.id;function Na(e){try{for(bt=0;bt<Ge.length;bt++){const t=Ge[bt];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),cr(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;bt<Ge.length;bt++){const t=Ge[bt];t&&(t.flags&=-2)}bt=-1,Ge.length=0,Vr(),Fr=null,(Ge.length||Sn.length)&&Na()}}let $e=null,Ba=null;function zr(e){const t=$e;return $e=e,Ba=e&&e.type.__scopeId||null,t}function pe(e,t=$e,n){if(!t||e._n)return e;const r=(...o)=>{r._d&&ni(-1);const s=zr(t);let i;try{i=e(...o)}finally{zr(s),r._d&&ni(1)}return i};return r._n=!0,r._c=!0,r._d=!0,r}function Ur(e,t){if($e===null)return e;const n=io($e),r=e.dirs||(e.dirs=[]);for(let o=0;o<t.length;o++){let[s,i,a,l=Se]=t[o];s&&(ie(s)&&(s={mounted:s,updated:s}),s.deep&&Ot(i),r.push({dir:s,instance:n,value:i,oldValue:void 0,arg:a,modifiers:l}))}return e}function Et(e,t,n,r){const o=e.dirs,s=t&&t.dirs;for(let i=0;i<o.length;i++){const a=o[i];s&&(a.oldValue=s[i].value);let l=a.dir[r];l&&(Ht(),gt(l,n,8,[e.el,a,e,t]),Mt())}}const _u=Symbol("_vte"),ja=e=>e.__isTeleport,zt=Symbol("_leaveCb"),Sr=Symbol("_enterCb");function yu(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return st(()=>{e.isMounted=!0}),hs(()=>{e.isUnmounting=!0}),e}const it=[Function,Array],Fa={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:it,onEnter:it,onAfterEnter:it,onEnterCancelled:it,onBeforeLeave:it,onLeave:it,onAfterLeave:it,onLeaveCancelled:it,onBeforeAppear:it,onAppear:it,onAfterAppear:it,onAppearCancelled:it},Va=e=>{const t=e.subTree;return t.component?Va(t.component):t},bu={name:"BaseTransition",props:Fa,setup(e,{slots:t}){const n=hn(),r=yu();return()=>{const o=t.default&&Wa(t.default(),!0);if(!o||!o.length)return;const s=za(o),i=he(e),{mode:a}=i;if(r.isLeaving)return wo(s);const l=zs(s);if(!l)return wo(s);let c=Vo(l,i,r,n,f=>c=f);l.type!==Be&&Xn(l,c);let u=n.subTree&&zs(n.subTree);if(u&&u.type!==Be&&!sn(l,u)&&Va(n).type!==Be){let f=Vo(u,i,r,n);if(Xn(u,f),a==="out-in"&&l.type!==Be)return r.isLeaving=!0,f.afterLeave=()=>{r.isLeaving=!1,n.job.flags&8||n.update(),delete f.afterLeave,u=void 0},wo(s);a==="in-out"&&l.type!==Be?f.delayLeave=(p,g,_)=>{const b=Ua(r,u);b[String(u.key)]=u,p[zt]=()=>{g(),p[zt]=void 0,delete c.delayedLeave,u=void 0},c.delayedLeave=()=>{_(),delete c.delayedLeave,u=void 0}}:u=void 0}else u&&(u=void 0);return s}}};function za(e){let t=e[0];if(e.length>1){for(const n of e)if(n.type!==Be){t=n;break}}return t}const Eu=bu;function Ua(e,t){const{leavingVNodes:n}=e;let r=n.get(t.type);return r||(r=Object.create(null),n.set(t.type,r)),r}function Vo(e,t,n,r,o){const{appear:s,mode:i,persisted:a=!1,onBeforeEnter:l,onEnter:c,onAfterEnter:u,onEnterCancelled:f,onBeforeLeave:p,onLeave:g,onAfterLeave:_,onLeaveCancelled:b,onBeforeAppear:w,onAppear:A,onAfterAppear:k,onAppearCancelled:m}=t,y=String(e.key),N=Ua(n,e),Y=(W,I)=>{W&&gt(W,r,9,I)},M=(W,I)=>{const Q=I[1];Y(W,I),se(W)?W.every(E=>E.length<=1)&&Q():W.length<=1&&Q()},S={mode:i,persisted:a,beforeEnter(W){let I=l;if(!n.isMounted)if(s)I=w||l;else return;W[zt]&&W[zt](!0);const Q=N[y];Q&&sn(e,Q)&&Q.el[zt]&&Q.el[zt](),Y(I,[W])},enter(W){let I=c,Q=u,E=f;if(!n.isMounted)if(s)I=A||c,Q=k||u,E=m||f;else return;let H=!1;const D=W[Sr]=X=>{H||(H=!0,X?Y(E,[W]):Y(Q,[W]),S.delayedLeave&&S.delayedLeave(),W[Sr]=void 0)};I?M(I,[W,D]):D()},leave(W,I){const Q=String(e.key);if(W[Sr]&&W[Sr](!0),n.isUnmounting)return I();Y(p,[W]);let E=!1;const H=W[zt]=D=>{E||(E=!0,I(),D?Y(b,[W]):Y(_,[W]),W[zt]=void 0,N[Q]===e&&delete N[Q])};N[Q]=e,g?M(g,[W,H]):H()},clone(W){const I=Vo(W,t,n,r,o);return o&&o(I),I}};return S}function wo(e){if(dr(e))return e=Zt(e),e.children=null,e}function zs(e){if(!dr(e))return ja(e.type)&&e.children?za(e.children):e;if(e.component)return e.component.subTree;const{shapeFlag:t,children:n}=e;if(n){if(t&16)return n[0];if(t&32&&ie(n.default))return n.default()}}function Xn(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Xn(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function Wa(e,t=!1,n){let r=[],o=0;for(let s=0;s<e.length;s++){let i=e[s];const a=n==null?i.key:String(n)+String(i.key!=null?i.key:s);i.type===ve?(i.patchFlag&128&&o++,r=r.concat(Wa(i.children,t,a))):(t||i.type!==Be)&&r.push(a!=null?Zt(i,{key:a}):i)}if(o>1)for(let s=0;s<r.length;s++)r[s].patchFlag=-2;return r}/*! #__NO_SIDE_EFFECTS__ */function me(e,t){return ie(e)?Me({name:e.name},t,{setup:e}):e}function ds(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function Us(e){const t=hn(),n=Pe(null);if(t){const o=t.refs===Se?t.refs={}:t.refs;Object.defineProperty(o,e,{enumerable:!0,get:()=>n.value,set:s=>n.value=s})}return n}function Cn(e,t,n,r,o=!1){if(se(e)){e.forEach((_,b)=>Cn(_,t&&(se(t)?t[b]:t),n,r,o));return}if(cn(r)&&!o){r.shapeFlag&512&&r.type.__asyncResolved&&r.component.subTree.component&&Cn(e,t,n,r.component.subTree);return}const s=r.shapeFlag&4?io(r.component):r.el,i=o?null:s,{i:a,r:l}=e,c=t&&t.r,u=a.refs===Se?a.refs={}:a.refs,f=a.setupState,p=he(f),g=f===Se?()=>!1:_=>ye(p,_);if(c!=null&&c!==l&&(ke(c)?(u[c]=null,g(c)&&(f[c]=null)):Oe(c)&&(c.value=null)),ie(l))cr(l,a,12,[i,u]);else{const _=ke(l),b=Oe(l);if(_||b){const w=()=>{if(e.f){const A=_?g(l)?f[l]:u[l]:l.value;o?se(A)&&ss(A,s):se(A)?A.includes(s)||A.push(s):_?(u[l]=[s],g(l)&&(f[l]=u[l])):(l.value=[s],e.k&&(u[e.k]=l.value))}else _?(u[l]=i,g(l)&&(f[l]=i)):b&&(l.value=i,e.k&&(u[e.k]=i))};i?(w.id=-1,tt(w,n)):w()}}}let Ws=!1;const gn=()=>{Ws||(console.error("Hydration completed but contains mismatches."),Ws=!0)},wu=e=>e.namespaceURI.includes("svg")&&e.tagName!=="foreignObject",Su=e=>e.namespaceURI.includes("MathML"),Cr=e=>{if(e.nodeType===1){if(wu(e))return"svg";if(Su(e))return"mathml"}},yn=e=>e.nodeType===8;function Cu(e){const{mt:t,p:n,o:{patchProp:r,createText:o,nextSibling:s,parentNode:i,remove:a,insert:l,createComment:c}}=e,u=(m,y)=>{if(!y.hasChildNodes()){n(null,m,y),Vr(),y._vnode=m;return}f(y.firstChild,m,null,null,null),Vr(),y._vnode=m},f=(m,y,N,Y,M,S=!1)=>{S=S||!!y.dynamicChildren;const W=yn(m)&&m.data==="[",I=()=>b(m,y,N,Y,M,W),{type:Q,ref:E,shapeFlag:H,patchFlag:D}=y;let X=m.nodeType;y.el=m,D===-2&&(S=!1,y.dynamicChildren=null);let L=null;switch(Q){case fn:X!==3?y.children===""?(l(y.el=o(""),i(m),m),L=m):L=I():(m.data!==y.children&&(gn(),m.data=y.children),L=s(m));break;case Be:k(m)?(L=s(m),A(y.el=m.content.firstChild,m,N)):X!==8||W?L=I():L=s(m);break;case qn:if(W&&(m=s(m),X=m.nodeType),X===1||X===3){L=m;const re=!y.children.length;for(let K=0;K<y.staticCount;K++)re&&(y.children+=L.nodeType===1?L.outerHTML:L.data),K===y.staticCount-1&&(y.anchor=L),L=s(L);return W?s(L):L}else I();break;case ve:W?L=_(m,y,N,Y,M,S):L=I();break;default:if(H&1)(X!==1||y.type.toLowerCase()!==m.tagName.toLowerCase())&&!k(m)?L=I():L=p(m,y,N,Y,M,S);else if(H&6){y.slotScopeIds=M;const re=i(m);if(W?L=w(m):yn(m)&&m.data==="teleport start"?L=w(m,m.data,"teleport end"):L=s(m),t(y,re,null,N,Y,Cr(re),S),cn(y)&&!y.type.__asyncResolved){let K;W?(K=J(ve),K.anchor=L?L.previousSibling:re.lastChild):K=m.nodeType===3?ot(""):J("div"),K.el=m,y.component.subTree=K}}else H&64?X!==8?L=I():L=y.type.hydrate(m,y,N,Y,M,S,e,g):H&128&&(L=y.type.hydrate(m,y,N,Y,Cr(i(m)),M,S,e,f))}return E!=null&&Cn(E,null,Y,y),L},p=(m,y,N,Y,M,S)=>{S=S||!!y.dynamicChildren;const{type:W,props:I,patchFlag:Q,shapeFlag:E,dirs:H,transition:D}=y,X=W==="input"||W==="option";if(X||Q!==-1){H&&Et(y,null,N,"created");let L=!1;if(k(m)){L=ul(null,D)&&N&&N.vnode.props&&N.vnode.props.appear;const K=m.content.firstChild;if(L){const be=K.getAttribute("class");be&&(K.$cls=be),D.beforeEnter(K)}A(K,m,N),y.el=m=K}if(E&16&&!(I&&(I.innerHTML||I.textContent))){let K=g(m.firstChild,y,m,N,Y,M,S);for(;K;){Tr(m,1)||gn();const be=K;K=K.nextSibling,a(be)}}else if(E&8){let K=y.children;K[0]===`
`&&(m.tagName==="PRE"||m.tagName==="TEXTAREA")&&(K=K.slice(1)),m.textContent!==K&&(Tr(m,0)||gn(),m.textContent=y.children)}if(I){if(X||!S||Q&48){const K=m.tagName.includes("-");for(const be in I)(X&&(be.endsWith("value")||be==="indeterminate")||ir(be)&&!wn(be)||be[0]==="."||K)&&r(m,be,null,I[be],void 0,N)}else if(I.onClick)r(m,"onClick",null,I.onClick,void 0,N);else if(Q&4&&ln(I.style))for(const K in I.style)I.style[K]}let re;(re=I&&I.onVnodeBeforeMount)&&at(re,N,y),H&&Et(y,null,N,"beforeMount"),((re=I&&I.onVnodeMounted)||H||L)&&vl(()=>{re&&at(re,N,y),L&&D.enter(m),H&&Et(y,null,N,"mounted")},Y)}return m.nextSibling},g=(m,y,N,Y,M,S,W)=>{W=W||!!y.dynamicChildren;const I=y.children,Q=I.length;for(let E=0;E<Q;E++){const H=W?I[E]:I[E]=lt(I[E]),D=H.type===fn;m?(D&&!W&&E+1<Q&&lt(I[E+1]).type===fn&&(l(o(m.data.slice(H.children.length)),N,s(m)),m.data=H.children),m=f(m,H,Y,M,S,W)):D&&!H.children?l(H.el=o(""),N):(Tr(N,1)||gn(),n(null,H,N,null,Y,M,Cr(N),S))}return m},_=(m,y,N,Y,M,S)=>{const{slotScopeIds:W}=y;W&&(M=M?M.concat(W):W);const I=i(m),Q=g(s(m),y,I,N,Y,M,S);return Q&&yn(Q)&&Q.data==="]"?s(y.anchor=Q):(gn(),l(y.anchor=c("]"),I,Q),Q)},b=(m,y,N,Y,M,S)=>{if(Tr(m.parentElement,1)||gn(),y.el=null,S){const Q=w(m);for(;;){const E=s(m);if(E&&E!==Q)a(E);else break}}const W=s(m),I=i(m);return a(m),n(null,y,I,W,N,Y,Cr(I),M),N&&(N.vnode.el=y.el,gl(N,y.el)),W},w=(m,y="[",N="]")=>{let Y=0;for(;m;)if(m=s(m),m&&yn(m)&&(m.data===y&&Y++,m.data===N)){if(Y===0)return s(m);Y--}return m},A=(m,y,N)=>{const Y=y.parentNode;Y&&Y.replaceChild(m,y);let M=N;for(;M;)M.vnode.el===y&&(M.vnode.el=M.subTree.el=m),M=M.parent},k=m=>m.nodeType===1&&m.tagName==="TEMPLATE";return[u,f]}const qs="data-allow-mismatch",Tu={0:"text",1:"children",2:"class",3:"style",4:"attribute"};function Tr(e,t){if(t===0||t===1)for(;e&&!e.hasAttribute(qs);)e=e.parentElement;const n=e&&e.getAttribute(qs);if(n==null)return!1;if(n==="")return!0;{const r=n.split(",");return t===0&&r.includes("children")?!0:r.includes(Tu[t])}}Qr().requestIdleCallback;Qr().cancelIdleCallback;function xu(e,t){if(yn(e)&&e.data==="["){let n=1,r=e.nextSibling;for(;r;){if(r.nodeType===1){if(t(r)===!1)break}else if(yn(r))if(r.data==="]"){if(--n===0)break}else r.data==="["&&n++;r=r.nextSibling}}else t(e)}const cn=e=>!!e.type.__asyncLoader;/*! #__NO_SIDE_EFFECTS__ */function Au(e){ie(e)&&(e={loader:e});const{loader:t,loadingComponent:n,errorComponent:r,delay:o=200,hydrate:s,timeout:i,suspensible:a=!0,onError:l}=e;let c=null,u,f=0;const p=()=>(f++,c=null,g()),g=()=>{let _;return c||(_=c=t().catch(b=>{if(b=b instanceof Error?b:new Error(String(b)),l)return new Promise((w,A)=>{l(b,()=>w(p()),()=>A(b),f+1)});throw b}).then(b=>_!==c&&c?c:(b&&(b.__esModule||b[Symbol.toStringTag]==="Module")&&(b=b.default),u=b,b)))};return me({name:"AsyncComponentWrapper",__asyncLoader:g,__asyncHydrate(_,b,w){const A=s?()=>{const m=s(()=>{w()},y=>xu(_,y));m&&(b.bum||(b.bum=[])).push(m),(b.u||(b.u=[])).push(()=>!0)}:w;u?A():g().then(()=>!b.isUnmounted&&A())},get __asyncResolved(){return u},setup(){const _=Re;if(ds(_),u)return()=>So(u,_);const b=m=>{c=null,ur(m,_,13,!r)};if(a&&_.suspense||kn)return g().then(m=>()=>So(m,_)).catch(m=>(b(m),()=>r?J(r,{error:m}):null));const w=qe(!1),A=qe(),k=qe(!!o);return o&&setTimeout(()=>{k.value=!1},o),i!=null&&setTimeout(()=>{if(!w.value&&!A.value){const m=new Error(`Async component timed out after ${i}ms.`);b(m),A.value=m}},i),g().then(()=>{w.value=!0,_.parent&&dr(_.parent.vnode)&&_.parent.update()}).catch(m=>{b(m),A.value=m}),()=>{if(w.value&&u)return So(u,_);if(A.value&&r)return J(r,{error:A.value});if(n&&!k.value)return J(n)}}})}function So(e,t){const{ref:n,props:r,children:o,ce:s}=t.vnode,i=J(e,r,o);return i.ref=n,i.ce=s,delete t.vnode.ce,i}const dr=e=>e.type.__isKeepAlive;function ku(e,t){qa(e,"a",t)}function Lu(e,t){qa(e,"da",t)}function qa(e,t,n=Re){const r=e.__wdc||(e.__wdc=()=>{let o=n;for(;o;){if(o.isDeactivated)return;o=o.parent}return e()});if(ro(t,r,n),n){let o=n.parent;for(;o&&o.parent;)dr(o.parent.vnode)&&Pu(r,t,n,o),o=o.parent}}function Pu(e,t,n,r){const o=ro(t,e,r,!0);hr(()=>{ss(r[t],o)},n)}function ro(e,t,n=Re,r=!1){if(n){const o=n[e]||(n[e]=[]),s=t.__weh||(t.__weh=(...i)=>{Ht();const a=pr(n),l=gt(t,n,e,i);return a(),Mt(),l});return r?o.unshift(s):o.push(s),s}}const Nt=e=>(t,n=Re)=>{(!kn||e==="sp")&&ro(e,(...r)=>t(...r),n)},Ru=Nt("bm"),st=Nt("m"),$u=Nt("bu"),Ou=Nt("u"),hs=Nt("bum"),hr=Nt("um"),Iu=Nt("sp"),Hu=Nt("rtg"),Mu=Nt("rtc");function Du(e,t=Re){ro("ec",e,t)}const Ka="components";function Ga(e,t){return Ja(Ka,e,!0,t)||e}const Za=Symbol.for("v-ndc");function Ks(e){return ke(e)?Ja(Ka,e,!1)||e:e||Za}function Ja(e,t,n=!0,r=!1){const o=$e||Re;if(o){const s=o.type;{const a=wf(s,!1);if(a&&(a===t||a===Qe(t)||a===ar(Qe(t))))return s}const i=Gs(o[e]||s[e],t)||Gs(o.appContext[e],t);return!i&&r?s:i}}function Gs(e,t){return e&&(e[t]||e[Qe(t)]||e[ar(Qe(t))])}function rt(e,t,n,r){let o;const s=n,i=se(e);if(i||ke(e)){const a=i&&ln(e);let l=!1,c=!1;a&&(l=!ct(e),c=Gt(e),e=eo(e)),o=new Array(e.length);for(let u=0,f=e.length;u<f;u++)o[u]=t(l?c?Br(Ne(e[u])):Ne(e[u]):e[u],u,void 0,s)}else if(typeof e=="number"){o=new Array(e);for(let a=0;a<e;a++)o[a]=t(a+1,a,void 0,s)}else if(xe(e))if(e[Symbol.iterator])o=Array.from(e,(a,l)=>t(a,l,void 0,s));else{const a=Object.keys(e);o=new Array(a.length);for(let l=0,c=a.length;l<c;l++){const u=a[l];o[l]=t(e[u],u,l,s)}}else o=[];return o}function Nu(e,t){for(let n=0;n<t.length;n++){const r=t[n];if(se(r))for(let o=0;o<r.length;o++)e[r[o].name]=r[o].fn;else r&&(e[r.name]=r.key?(...o)=>{const s=r.fn(...o);return s&&(s.key=r.key),s}:r.fn)}return e}function Ae(e,t,n={},r,o){if($e.ce||$e.parent&&cn($e.parent)&&$e.parent.ce)return t!=="default"&&(n.name=t),T(),ge(ve,null,[J("slot",n,r&&r())],64);let s=e[t];s&&s._c&&(s._d=!1),T();const i=s&&Ya(s(n)),a=n.key||i&&i.key,l=ge(ve,{key:(a&&!Dt(a)?a:`_${t}`)+(!i&&r?"_fb":"")},i||(r?r():[]),i&&e._===1?64:-2);return l.scopeId&&(l.slotScopeIds=[l.scopeId+"-s"]),s&&s._c&&(s._d=!0),l}function Ya(e){return e.some(t=>tr(t)?!(t.type===Be||t.type===ve&&!Ya(t.children)):!0)?e:null}const zo=e=>e?bl(e)?io(e):zo(e.parent):null,Wn=Me(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>zo(e.parent),$root:e=>zo(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Xa(e),$forceUpdate:e=>e.f||(e.f=()=>{fs(e.update)}),$nextTick:e=>e.n||(e.n=fr.bind(e.proxy)),$watch:e=>sf.bind(e)}),Co=(e,t)=>e!==Se&&!e.__isScriptSetup&&ye(e,t),Bu={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:r,data:o,props:s,accessCache:i,type:a,appContext:l}=e;let c;if(t[0]!=="$"){const g=i[t];if(g!==void 0)switch(g){case 1:return r[t];case 2:return o[t];case 4:return n[t];case 3:return s[t]}else{if(Co(r,t))return i[t]=1,r[t];if(o!==Se&&ye(o,t))return i[t]=2,o[t];if((c=e.propsOptions[0])&&ye(c,t))return i[t]=3,s[t];if(n!==Se&&ye(n,t))return i[t]=4,n[t];Uo&&(i[t]=0)}}const u=Wn[t];let f,p;if(u)return t==="$attrs"&&We(e.attrs,"get",""),u(e);if((f=a.__cssModules)&&(f=f[t]))return f;if(n!==Se&&ye(n,t))return i[t]=4,n[t];if(p=l.config.globalProperties,ye(p,t))return p[t]},set({_:e},t,n){const{data:r,setupState:o,ctx:s}=e;return Co(o,t)?(o[t]=n,!0):r!==Se&&ye(r,t)?(r[t]=n,!0):ye(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(s[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:r,appContext:o,propsOptions:s}},i){let a;return!!n[i]||e!==Se&&ye(e,i)||Co(t,i)||(a=s[0])&&ye(a,i)||ye(r,i)||ye(Wn,i)||ye(o.config.globalProperties,i)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:ye(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Zs(e){return se(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let Uo=!0;function ju(e){const t=Xa(e),n=e.proxy,r=e.ctx;Uo=!1,t.beforeCreate&&Js(t.beforeCreate,e,"bc");const{data:o,computed:s,methods:i,watch:a,provide:l,inject:c,created:u,beforeMount:f,mounted:p,beforeUpdate:g,updated:_,activated:b,deactivated:w,beforeDestroy:A,beforeUnmount:k,destroyed:m,unmounted:y,render:N,renderTracked:Y,renderTriggered:M,errorCaptured:S,serverPrefetch:W,expose:I,inheritAttrs:Q,components:E,directives:H,filters:D}=t;if(c&&Fu(c,r,null),i)for(const re in i){const K=i[re];ie(K)&&(r[re]=K.bind(n))}if(o){const re=o.call(n,n);xe(re)&&(e.data=lr(re))}if(Uo=!0,s)for(const re in s){const K=s[re],be=ie(K)?K.bind(n,n):ie(K.get)?K.get.bind(n,n):St,De=!ie(K)&&ie(K.set)?K.set.bind(n):St,Ze=O({get:be,set:De});Object.defineProperty(r,re,{enumerable:!0,configurable:!0,get:()=>Ze.value,set:Ve=>Ze.value=Ve})}if(a)for(const re in a)Qa(a[re],r,n,re);if(l){const re=ie(l)?l.call(n):l;Reflect.ownKeys(re).forEach(K=>{Kt(K,re[K])})}u&&Js(u,e,"c");function L(re,K){se(K)?K.forEach(be=>re(be.bind(n))):K&&re(K.bind(n))}if(L(Ru,f),L(st,p),L($u,g),L(Ou,_),L(ku,b),L(Lu,w),L(Du,S),L(Mu,Y),L(Hu,M),L(hs,k),L(hr,y),L(Iu,W),se(I))if(I.length){const re=e.exposed||(e.exposed={});I.forEach(K=>{Object.defineProperty(re,K,{get:()=>n[K],set:be=>n[K]=be})})}else e.exposed||(e.exposed={});N&&e.render===St&&(e.render=N),Q!=null&&(e.inheritAttrs=Q),E&&(e.components=E),H&&(e.directives=H),W&&ds(e)}function Fu(e,t,n=St){se(e)&&(e=Wo(e));for(const r in e){const o=e[r];let s;xe(o)?"default"in o?s=Fe(o.from||r,o.default,!0):s=Fe(o.from||r):s=Fe(o),Oe(s)?Object.defineProperty(t,r,{enumerable:!0,configurable:!0,get:()=>s.value,set:i=>s.value=i}):t[r]=s}}function Js(e,t,n){gt(se(e)?e.map(r=>r.bind(t.proxy)):e.bind(t.proxy),t,n)}function Qa(e,t,n,r){let o=r.includes(".")?hl(n,r):()=>n[r];if(ke(e)){const s=t[e];ie(s)&&ut(o,s)}else if(ie(e))ut(o,e.bind(n));else if(xe(e))if(se(e))e.forEach(s=>Qa(s,t,n,r));else{const s=ie(e.handler)?e.handler.bind(n):t[e.handler];ie(s)&&ut(o,s,e)}}function Xa(e){const t=e.type,{mixins:n,extends:r}=t,{mixins:o,optionsCache:s,config:{optionMergeStrategies:i}}=e.appContext,a=s.get(t);let l;return a?l=a:!o.length&&!n&&!r?l=t:(l={},o.length&&o.forEach(c=>Wr(l,c,i,!0)),Wr(l,t,i)),xe(t)&&s.set(t,l),l}function Wr(e,t,n,r=!1){const{mixins:o,extends:s}=t;s&&Wr(e,s,n,!0),o&&o.forEach(i=>Wr(e,i,n,!0));for(const i in t)if(!(r&&i==="expose")){const a=Vu[i]||n&&n[i];e[i]=a?a(e[i],t[i]):t[i]}return e}const Vu={data:Ys,props:Qs,emits:Qs,methods:Fn,computed:Fn,beforeCreate:Ke,created:Ke,beforeMount:Ke,mounted:Ke,beforeUpdate:Ke,updated:Ke,beforeDestroy:Ke,beforeUnmount:Ke,destroyed:Ke,unmounted:Ke,activated:Ke,deactivated:Ke,errorCaptured:Ke,serverPrefetch:Ke,components:Fn,directives:Fn,watch:Uu,provide:Ys,inject:zu};function Ys(e,t){return t?e?function(){return Me(ie(e)?e.call(this,this):e,ie(t)?t.call(this,this):t)}:t:e}function zu(e,t){return Fn(Wo(e),Wo(t))}function Wo(e){if(se(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function Ke(e,t){return e?[...new Set([].concat(e,t))]:t}function Fn(e,t){return e?Me(Object.create(null),e,t):t}function Qs(e,t){return e?se(e)&&se(t)?[...new Set([...e,...t])]:Me(Object.create(null),Zs(e),Zs(t??{})):t}function Uu(e,t){if(!e)return t;if(!t)return e;const n=Me(Object.create(null),e);for(const r in t)n[r]=Ke(e[r],t[r]);return n}function el(){return{app:null,config:{isNativeTag:Cc,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Wu=0;function qu(e,t){return function(r,o=null){ie(r)||(r=Me({},r)),o!=null&&!xe(o)&&(o=null);const s=el(),i=new WeakSet,a=[];let l=!1;const c=s.app={_uid:Wu++,_component:r,_props:o,_container:null,_context:s,_instance:null,version:Cf,get config(){return s.config},set config(u){},use(u,...f){return i.has(u)||(u&&ie(u.install)?(i.add(u),u.install(c,...f)):ie(u)&&(i.add(u),u(c,...f))),c},mixin(u){return s.mixins.includes(u)||s.mixins.push(u),c},component(u,f){return f?(s.components[u]=f,c):s.components[u]},directive(u,f){return f?(s.directives[u]=f,c):s.directives[u]},mount(u,f,p){if(!l){const g=c._ceVNode||J(r,o);return g.appContext=s,p===!0?p="svg":p===!1&&(p=void 0),f&&t?t(g,u):e(g,u,p),l=!0,c._container=u,u.__vue_app__=c,io(g.component)}},onUnmount(u){a.push(u)},unmount(){l&&(gt(a,c._instance,16),e(null,c._container),delete c._container.__vue_app__)},provide(u,f){return s.provides[u]=f,c},runWithContext(u){const f=un;un=c;try{return u()}finally{un=f}}};return c}}let un=null;function Kt(e,t){if(Re){let n=Re.provides;const r=Re.parent&&Re.parent.provides;r===n&&(n=Re.provides=Object.create(r)),n[e]=t}}function Fe(e,t,n=!1){const r=Re||$e;if(r||un){let o=un?un._context.provides:r?r.parent==null||r.ce?r.vnode.appContext&&r.vnode.appContext.provides:r.parent.provides:void 0;if(o&&e in o)return o[e];if(arguments.length>1)return n&&ie(t)?t.call(r&&r.proxy):t}}function tl(){return!!(Re||$e||un)}const nl={},rl=()=>Object.create(nl),ol=e=>Object.getPrototypeOf(e)===nl;function Ku(e,t,n,r=!1){const o={},s=rl();e.propsDefaults=Object.create(null),sl(e,t,o,s);for(const i in e.propsOptions[0])i in o||(o[i]=void 0);n?e.props=r?o:La(o):e.type.props?e.props=o:e.props=s,e.attrs=s}function Gu(e,t,n,r){const{props:o,attrs:s,vnode:{patchFlag:i}}=e,a=he(o),[l]=e.propsOptions;let c=!1;if((r||i>0)&&!(i&16)){if(i&8){const u=e.vnode.dynamicProps;for(let f=0;f<u.length;f++){let p=u[f];if(oo(e.emitsOptions,p))continue;const g=t[p];if(l)if(ye(s,p))g!==s[p]&&(s[p]=g,c=!0);else{const _=Qe(p);o[_]=qo(l,a,_,g,e,!1)}else g!==s[p]&&(s[p]=g,c=!0)}}}else{sl(e,t,o,s)&&(c=!0);let u;for(const f in a)(!t||!ye(t,f)&&((u=Jt(f))===f||!ye(t,u)))&&(l?n&&(n[f]!==void 0||n[u]!==void 0)&&(o[f]=qo(l,a,f,void 0,e,!0)):delete o[f]);if(s!==a)for(const f in s)(!t||!ye(t,f))&&(delete s[f],c=!0)}c&&$t(e.attrs,"set","")}function sl(e,t,n,r){const[o,s]=e.propsOptions;let i=!1,a;if(t)for(let l in t){if(wn(l))continue;const c=t[l];let u;o&&ye(o,u=Qe(l))?!s||!s.includes(u)?n[u]=c:(a||(a={}))[u]=c:oo(e.emitsOptions,l)||(!(l in r)||c!==r[l])&&(r[l]=c,i=!0)}if(s){const l=he(n),c=a||Se;for(let u=0;u<s.length;u++){const f=s[u];n[f]=qo(o,l,f,c[f],e,!ye(c,f))}}return i}function qo(e,t,n,r,o,s){const i=e[n];if(i!=null){const a=ye(i,"default");if(a&&r===void 0){const l=i.default;if(i.type!==Function&&!i.skipFactory&&ie(l)){const{propsDefaults:c}=o;if(n in c)r=c[n];else{const u=pr(o);r=c[n]=l.call(null,t),u()}}else r=l;o.ce&&o.ce._setProp(n,r)}i[0]&&(s&&!a?r=!1:i[1]&&(r===""||r===Jt(n))&&(r=!0))}return r}const Zu=new WeakMap;function il(e,t,n=!1){const r=n?Zu:t.propsCache,o=r.get(e);if(o)return o;const s=e.props,i={},a=[];let l=!1;if(!ie(e)){const u=f=>{l=!0;const[p,g]=il(f,t,!0);Me(i,p),g&&a.push(...g)};!n&&t.mixins.length&&t.mixins.forEach(u),e.extends&&u(e.extends),e.mixins&&e.mixins.forEach(u)}if(!s&&!l)return xe(e)&&r.set(e,bn),bn;if(se(s))for(let u=0;u<s.length;u++){const f=Qe(s[u]);Xs(f)&&(i[f]=Se)}else if(s)for(const u in s){const f=Qe(u);if(Xs(f)){const p=s[u],g=i[f]=se(p)||ie(p)?{type:p}:Me({},p),_=g.type;let b=!1,w=!0;if(se(_))for(let A=0;A<_.length;++A){const k=_[A],m=ie(k)&&k.name;if(m==="Boolean"){b=!0;break}else m==="String"&&(w=!1)}else b=ie(_)&&_.name==="Boolean";g[0]=b,g[1]=w,(b||ye(g,"default"))&&a.push(f)}}const c=[i,a];return xe(e)&&r.set(e,c),c}function Xs(e){return e[0]!=="$"&&!wn(e)}const ps=e=>e[0]==="_"||e==="$stable",gs=e=>se(e)?e.map(lt):[lt(e)],Ju=(e,t,n)=>{if(t._n)return t;const r=pe((...o)=>gs(t(...o)),n);return r._c=!1,r},al=(e,t,n)=>{const r=e._ctx;for(const o in e){if(ps(o))continue;const s=e[o];if(ie(s))t[o]=Ju(o,s,r);else if(s!=null){const i=gs(s);t[o]=()=>i}}},ll=(e,t)=>{const n=gs(t);e.slots.default=()=>n},cl=(e,t,n)=>{for(const r in t)(n||!ps(r))&&(e[r]=t[r])},Yu=(e,t,n)=>{const r=e.slots=rl();if(e.vnode.shapeFlag&32){const o=t.__;o&&No(r,"__",o,!0);const s=t._;s?(cl(r,t,n),n&&No(r,"_",s,!0)):al(t,r)}else t&&ll(e,t)},Qu=(e,t,n)=>{const{vnode:r,slots:o}=e;let s=!0,i=Se;if(r.shapeFlag&32){const a=t._;a?n&&a===1?s=!1:cl(o,t,n):(s=!t.$stable,al(t,o)),i=t}else t&&(ll(e,t),i={default:1});if(s)for(const a in o)!ps(a)&&i[a]==null&&delete o[a]},tt=vl;function Xu(e){return ef(e,Cu)}function ef(e,t){const n=Qr();n.__VUE__=!0;const{insert:r,remove:o,patchProp:s,createElement:i,createText:a,createComment:l,setText:c,setElementText:u,parentNode:f,nextSibling:p,setScopeId:g=St,insertStaticContent:_}=e,b=(d,h,v,P=null,C=null,R=null,q=void 0,z=null,V=!!h.dynamicChildren)=>{if(d===h)return;d&&!sn(d,h)&&(P=x(d),Ve(d,C,R,!0),d=null),h.patchFlag===-2&&(V=!1,h.dynamicChildren=null);const{type:$,ref:oe,shapeFlag:G}=h;switch($){case fn:w(d,h,v,P);break;case Be:A(d,h,v,P);break;case qn:d==null&&k(h,v,P,q);break;case ve:E(d,h,v,P,C,R,q,z,V);break;default:G&1?N(d,h,v,P,C,R,q,z,V):G&6?H(d,h,v,P,C,R,q,z,V):(G&64||G&128)&&$.process(d,h,v,P,C,R,q,z,V,ee)}oe!=null&&C?Cn(oe,d&&d.ref,R,h||d,!h):oe==null&&d&&d.ref!=null&&Cn(d.ref,null,R,d,!0)},w=(d,h,v,P)=>{if(d==null)r(h.el=a(h.children),v,P);else{const C=h.el=d.el;h.children!==d.children&&c(C,h.children)}},A=(d,h,v,P)=>{d==null?r(h.el=l(h.children||""),v,P):h.el=d.el},k=(d,h,v,P)=>{[d.el,d.anchor]=_(d.children,h,v,P,d.el,d.anchor)},m=({el:d,anchor:h},v,P)=>{let C;for(;d&&d!==h;)C=p(d),r(d,v,P),d=C;r(h,v,P)},y=({el:d,anchor:h})=>{let v;for(;d&&d!==h;)v=p(d),o(d),d=v;o(h)},N=(d,h,v,P,C,R,q,z,V)=>{h.type==="svg"?q="svg":h.type==="math"&&(q="mathml"),d==null?Y(h,v,P,C,R,q,z,V):W(d,h,C,R,q,z,V)},Y=(d,h,v,P,C,R,q,z)=>{let V,$;const{props:oe,shapeFlag:G,transition:ne,dirs:ae}=d;if(V=d.el=i(d.type,R,oe&&oe.is,oe),G&8?u(V,d.children):G&16&&S(d.children,V,null,P,C,To(d,R),q,z),ae&&Et(d,null,P,"created"),M(V,d,d.scopeId,q,P),oe){for(const Ce in oe)Ce!=="value"&&!wn(Ce)&&s(V,Ce,null,oe[Ce],R,P);"value"in oe&&s(V,"value",null,oe.value,R),($=oe.onVnodeBeforeMount)&&at($,P,d)}ae&&Et(d,null,P,"beforeMount");const de=ul(C,ne);de&&ne.beforeEnter(V),r(V,h,v),(($=oe&&oe.onVnodeMounted)||de||ae)&&tt(()=>{$&&at($,P,d),de&&ne.enter(V),ae&&Et(d,null,P,"mounted")},C)},M=(d,h,v,P,C)=>{if(v&&g(d,v),P)for(let R=0;R<P.length;R++)g(d,P[R]);if(C){let R=C.subTree;if(h===R||ml(R.type)&&(R.ssContent===h||R.ssFallback===h)){const q=C.vnode;M(d,q,q.scopeId,q.slotScopeIds,C.parent)}}},S=(d,h,v,P,C,R,q,z,V=0)=>{for(let $=V;$<d.length;$++){const oe=d[$]=z?Ut(d[$]):lt(d[$]);b(null,oe,h,v,P,C,R,q,z)}},W=(d,h,v,P,C,R,q)=>{const z=h.el=d.el;let{patchFlag:V,dynamicChildren:$,dirs:oe}=h;V|=d.patchFlag&16;const G=d.props||Se,ne=h.props||Se;let ae;if(v&&Xt(v,!1),(ae=ne.onVnodeBeforeUpdate)&&at(ae,v,h,d),oe&&Et(h,d,v,"beforeUpdate"),v&&Xt(v,!0),(G.innerHTML&&ne.innerHTML==null||G.textContent&&ne.textContent==null)&&u(z,""),$?I(d.dynamicChildren,$,z,v,P,To(h,C),R):q||K(d,h,z,null,v,P,To(h,C),R,!1),V>0){if(V&16)Q(z,G,ne,v,C);else if(V&2&&G.class!==ne.class&&s(z,"class",null,ne.class,C),V&4&&s(z,"style",G.style,ne.style,C),V&8){const de=h.dynamicProps;for(let Ce=0;Ce<de.length;Ce++){const Ee=de[Ce],Ye=G[Ee],ze=ne[Ee];(ze!==Ye||Ee==="value")&&s(z,Ee,Ye,ze,C,v)}}V&1&&d.children!==h.children&&u(z,h.children)}else!q&&$==null&&Q(z,G,ne,v,C);((ae=ne.onVnodeUpdated)||oe)&&tt(()=>{ae&&at(ae,v,h,d),oe&&Et(h,d,v,"updated")},P)},I=(d,h,v,P,C,R,q)=>{for(let z=0;z<h.length;z++){const V=d[z],$=h[z],oe=V.el&&(V.type===ve||!sn(V,$)||V.shapeFlag&198)?f(V.el):v;b(V,$,oe,null,P,C,R,q,!0)}},Q=(d,h,v,P,C)=>{if(h!==v){if(h!==Se)for(const R in h)!wn(R)&&!(R in v)&&s(d,R,h[R],null,C,P);for(const R in v){if(wn(R))continue;const q=v[R],z=h[R];q!==z&&R!=="value"&&s(d,R,z,q,C,P)}"value"in v&&s(d,"value",h.value,v.value,C)}},E=(d,h,v,P,C,R,q,z,V)=>{const $=h.el=d?d.el:a(""),oe=h.anchor=d?d.anchor:a("");let{patchFlag:G,dynamicChildren:ne,slotScopeIds:ae}=h;ae&&(z=z?z.concat(ae):ae),d==null?(r($,v,P),r(oe,v,P),S(h.children||[],v,oe,C,R,q,z,V)):G>0&&G&64&&ne&&d.dynamicChildren?(I(d.dynamicChildren,ne,v,C,R,q,z),(h.key!=null||C&&h===C.subTree)&&fl(d,h,!0)):K(d,h,v,oe,C,R,q,z,V)},H=(d,h,v,P,C,R,q,z,V)=>{h.slotScopeIds=z,d==null?h.shapeFlag&512?C.ctx.activate(h,v,P,q,V):D(h,v,P,C,R,q,V):X(d,h,V)},D=(d,h,v,P,C,R,q)=>{const z=d.component=vf(d,P,C);if(dr(d)&&(z.ctx.renderer=ee),_f(z,!1,q),z.asyncDep){if(C&&C.registerDep(z,L,q),!d.el){const V=z.subTree=J(Be);A(null,V,h,v)}}else L(z,d,h,v,C,R,q)},X=(d,h,v)=>{const P=h.component=d.component;if(ff(d,h,v))if(P.asyncDep&&!P.asyncResolved){re(P,h,v);return}else P.next=h,P.update();else h.el=d.el,P.vnode=h},L=(d,h,v,P,C,R,q)=>{const z=()=>{if(d.isMounted){let{next:G,bu:ne,u:ae,parent:de,vnode:Ce}=d;{const Xe=dl(d);if(Xe){G&&(G.el=Ce.el,re(d,G,q)),Xe.asyncDep.then(()=>{d.isUnmounted||z()});return}}let Ee=G,Ye;Xt(d,!1),G?(G.el=Ce.el,re(d,G,q)):G=Ce,ne&&mo(ne),(Ye=G.props&&G.props.onVnodeBeforeUpdate)&&at(Ye,de,G,Ce),Xt(d,!0);const ze=xo(d),dt=d.subTree;d.subTree=ze,b(dt,ze,f(dt.el),x(dt),d,C,R),G.el=ze.el,Ee===null&&gl(d,ze.el),ae&&tt(ae,C),(Ye=G.props&&G.props.onVnodeUpdated)&&tt(()=>at(Ye,de,G,Ce),C)}else{let G;const{el:ne,props:ae}=h,{bm:de,m:Ce,parent:Ee,root:Ye,type:ze}=d,dt=cn(h);if(Xt(d,!1),de&&mo(de),!dt&&(G=ae&&ae.onVnodeBeforeMount)&&at(G,Ee,h),Xt(d,!0),ne&&we){const Xe=()=>{d.subTree=xo(d),we(ne,d.subTree,d,C,null)};dt&&ze.__asyncHydrate?ze.__asyncHydrate(ne,d,Xe):Xe()}else{Ye.ce&&Ye.ce._def.shadowRoot!==!1&&Ye.ce._injectChildStyle(ze);const Xe=d.subTree=xo(d);b(null,Xe,v,P,d,C,R),h.el=Xe.el}if(Ce&&tt(Ce,C),!dt&&(G=ae&&ae.onVnodeMounted)){const Xe=h;tt(()=>at(G,Ee,Xe),C)}(h.shapeFlag&256||Ee&&cn(Ee.vnode)&&Ee.vnode.shapeFlag&256)&&d.a&&tt(d.a,C),d.isMounted=!0,h=v=P=null}};d.scope.on();const V=d.effect=new ha(z);d.scope.off();const $=d.update=V.run.bind(V),oe=d.job=V.runIfDirty.bind(V);oe.i=d,oe.id=d.uid,V.scheduler=()=>fs(oe),Xt(d,!0),$()},re=(d,h,v)=>{h.component=d;const P=d.vnode.props;d.vnode=h,d.next=null,Gu(d,h.props,P,v),Qu(d,h.children,v),Ht(),Vs(d),Mt()},K=(d,h,v,P,C,R,q,z,V=!1)=>{const $=d&&d.children,oe=d?d.shapeFlag:0,G=h.children,{patchFlag:ne,shapeFlag:ae}=h;if(ne>0){if(ne&128){De($,G,v,P,C,R,q,z,V);return}else if(ne&256){be($,G,v,P,C,R,q,z,V);return}}ae&8?(oe&16&&Je($,C,R),G!==$&&u(v,G)):oe&16?ae&16?De($,G,v,P,C,R,q,z,V):Je($,C,R,!0):(oe&8&&u(v,""),ae&16&&S(G,v,P,C,R,q,z,V))},be=(d,h,v,P,C,R,q,z,V)=>{d=d||bn,h=h||bn;const $=d.length,oe=h.length,G=Math.min($,oe);let ne;for(ne=0;ne<G;ne++){const ae=h[ne]=V?Ut(h[ne]):lt(h[ne]);b(d[ne],ae,v,null,C,R,q,z,V)}$>oe?Je(d,C,R,!0,!1,G):S(h,v,P,C,R,q,z,V,G)},De=(d,h,v,P,C,R,q,z,V)=>{let $=0;const oe=h.length;let G=d.length-1,ne=oe-1;for(;$<=G&&$<=ne;){const ae=d[$],de=h[$]=V?Ut(h[$]):lt(h[$]);if(sn(ae,de))b(ae,de,v,null,C,R,q,z,V);else break;$++}for(;$<=G&&$<=ne;){const ae=d[G],de=h[ne]=V?Ut(h[ne]):lt(h[ne]);if(sn(ae,de))b(ae,de,v,null,C,R,q,z,V);else break;G--,ne--}if($>G){if($<=ne){const ae=ne+1,de=ae<oe?h[ae].el:P;for(;$<=ne;)b(null,h[$]=V?Ut(h[$]):lt(h[$]),v,de,C,R,q,z,V),$++}}else if($>ne)for(;$<=G;)Ve(d[$],C,R,!0),$++;else{const ae=$,de=$,Ce=new Map;for($=de;$<=ne;$++){const et=h[$]=V?Ut(h[$]):lt(h[$]);et.key!=null&&Ce.set(et.key,$)}let Ee,Ye=0;const ze=ne-de+1;let dt=!1,Xe=0;const Hn=new Array(ze);for($=0;$<ze;$++)Hn[$]=0;for($=ae;$<=G;$++){const et=d[$];if(Ye>=ze){Ve(et,C,R,!0);continue}let yt;if(et.key!=null)yt=Ce.get(et.key);else for(Ee=de;Ee<=ne;Ee++)if(Hn[Ee-de]===0&&sn(et,h[Ee])){yt=Ee;break}yt===void 0?Ve(et,C,R,!0):(Hn[yt-de]=$+1,yt>=Xe?Xe=yt:dt=!0,b(et,h[yt],v,null,C,R,q,z,V),Ye++)}const Ds=dt?tf(Hn):bn;for(Ee=Ds.length-1,$=ze-1;$>=0;$--){const et=de+$,yt=h[et],Ns=et+1<oe?h[et+1].el:P;Hn[$]===0?b(null,yt,v,Ns,C,R,q,z,V):dt&&(Ee<0||$!==Ds[Ee]?Ze(yt,v,Ns,2):Ee--)}}},Ze=(d,h,v,P,C=null)=>{const{el:R,type:q,transition:z,children:V,shapeFlag:$}=d;if($&6){Ze(d.component.subTree,h,v,P);return}if($&128){d.suspense.move(h,v,P);return}if($&64){q.move(d,h,v,ee);return}if(q===ve){r(R,h,v);for(let G=0;G<V.length;G++)Ze(V[G],h,v,P);r(d.anchor,h,v);return}if(q===qn){m(d,h,v);return}if(P!==2&&$&1&&z)if(P===0)z.beforeEnter(R),r(R,h,v),tt(()=>z.enter(R),C);else{const{leave:G,delayLeave:ne,afterLeave:ae}=z,de=()=>{d.ctx.isUnmounted?o(R):r(R,h,v)},Ce=()=>{G(R,()=>{de(),ae&&ae()})};ne?ne(R,de,Ce):Ce()}else r(R,h,v)},Ve=(d,h,v,P=!1,C=!1)=>{const{type:R,props:q,ref:z,children:V,dynamicChildren:$,shapeFlag:oe,patchFlag:G,dirs:ne,cacheIndex:ae}=d;if(G===-2&&(C=!1),z!=null&&(Ht(),Cn(z,null,v,d,!0),Mt()),ae!=null&&(h.renderCache[ae]=void 0),oe&256){h.ctx.deactivate(d);return}const de=oe&1&&ne,Ce=!cn(d);let Ee;if(Ce&&(Ee=q&&q.onVnodeBeforeUnmount)&&at(Ee,h,d),oe&6)_t(d.component,v,P);else{if(oe&128){d.suspense.unmount(v,P);return}de&&Et(d,null,h,"beforeUnmount"),oe&64?d.type.remove(d,h,v,ee,P):$&&!$.hasOnce&&(R!==ve||G>0&&G&64)?Je($,h,v,!1,!0):(R===ve&&G&384||!C&&oe&16)&&Je(V,h,v),P&&Bt(d)}(Ce&&(Ee=q&&q.onVnodeUnmounted)||de)&&tt(()=>{Ee&&at(Ee,h,d),de&&Et(d,null,h,"unmounted")},v)},Bt=d=>{const{type:h,el:v,anchor:P,transition:C}=d;if(h===ve){jt(v,P);return}if(h===qn){y(d);return}const R=()=>{o(v),C&&!C.persisted&&C.afterLeave&&C.afterLeave()};if(d.shapeFlag&1&&C&&!C.persisted){const{leave:q,delayLeave:z}=C,V=()=>q(v,R);z?z(d.el,R,V):V()}else R()},jt=(d,h)=>{let v;for(;d!==h;)v=p(d),o(d),d=v;o(h)},_t=(d,h,v)=>{const{bum:P,scope:C,job:R,subTree:q,um:z,m:V,a:$,parent:oe,slots:{__:G}}=d;ei(V),ei($),P&&mo(P),oe&&se(G)&&G.forEach(ne=>{oe.renderCache[ne]=void 0}),C.stop(),R&&(R.flags|=8,Ve(q,d,h,v)),z&&tt(z,h),tt(()=>{d.isUnmounted=!0},h),h&&h.pendingBranch&&!h.isUnmounted&&d.asyncDep&&!d.asyncResolved&&d.suspenseId===h.pendingId&&(h.deps--,h.deps===0&&h.resolve())},Je=(d,h,v,P=!1,C=!1,R=0)=>{for(let q=R;q<d.length;q++)Ve(d[q],h,v,P,C)},x=d=>{if(d.shapeFlag&6)return x(d.component.subTree);if(d.shapeFlag&128)return d.suspense.next();const h=p(d.anchor||d.el),v=h&&h[_u];return v?p(v):h};let Z=!1;const U=(d,h,v)=>{d==null?h._vnode&&Ve(h._vnode,null,null,!0):b(h._vnode||null,d,h,null,null,null,v),h._vnode=d,Z||(Z=!0,Vs(),Vr(),Z=!1)},ee={p:b,um:Ve,m:Ze,r:Bt,mt:D,mc:S,pc:K,pbc:I,n:x,o:e};let fe,we;return t&&([fe,we]=t(ee)),{render:U,hydrate:fe,createApp:qu(U,fe)}}function To({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function Xt({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function ul(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function fl(e,t,n=!1){const r=e.children,o=t.children;if(se(r)&&se(o))for(let s=0;s<r.length;s++){const i=r[s];let a=o[s];a.shapeFlag&1&&!a.dynamicChildren&&((a.patchFlag<=0||a.patchFlag===32)&&(a=o[s]=Ut(o[s]),a.el=i.el),!n&&a.patchFlag!==-2&&fl(i,a)),a.type===fn&&(a.el=i.el),a.type===Be&&!a.el&&(a.el=i.el)}}function tf(e){const t=e.slice(),n=[0];let r,o,s,i,a;const l=e.length;for(r=0;r<l;r++){const c=e[r];if(c!==0){if(o=n[n.length-1],e[o]<c){t[r]=o,n.push(r);continue}for(s=0,i=n.length-1;s<i;)a=s+i>>1,e[n[a]]<c?s=a+1:i=a;c<e[n[s]]&&(s>0&&(t[r]=n[s-1]),n[s]=r)}}for(s=n.length,i=n[s-1];s-- >0;)n[s]=i,i=t[i];return n}function dl(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:dl(t)}function ei(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const nf=Symbol.for("v-scx"),rf=()=>Fe(nf);function of(e,t){return ms(e,null,t)}function ut(e,t,n){return ms(e,t,n)}function ms(e,t,n=Se){const{immediate:r,deep:o,flush:s,once:i}=n,a=Me({},n),l=t&&r||!t&&s!=="post";let c;if(kn){if(s==="sync"){const g=rf();c=g.__watcherHandles||(g.__watcherHandles=[])}else if(!l){const g=()=>{};return g.stop=St,g.resume=St,g.pause=St,g}}const u=Re;a.call=(g,_,b)=>gt(g,u,_,b);let f=!1;s==="post"?a.scheduler=g=>{tt(g,u&&u.suspense)}:s!=="sync"&&(f=!0,a.scheduler=(g,_)=>{_?g():fs(g)}),a.augmentJob=g=>{t&&(g.flags|=4),f&&(g.flags|=2,u&&(g.id=u.uid,g.i=u))};const p=pu(e,t,a);return kn&&(c?c.push(p):l&&p()),p}function sf(e,t,n){const r=this.proxy,o=ke(e)?e.includes(".")?hl(r,e):()=>r[e]:e.bind(r,r);let s;ie(t)?s=t:(s=t.handler,n=t);const i=pr(this),a=ms(o,s.bind(r),n);return i(),a}function hl(e,t){const n=t.split(".");return()=>{let r=e;for(let o=0;o<n.length&&r;o++)r=r[n[o]];return r}}const af=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Qe(t)}Modifiers`]||e[`${Jt(t)}Modifiers`];function lf(e,t,...n){if(e.isUnmounted)return;const r=e.vnode.props||Se;let o=n;const s=t.startsWith("update:"),i=s&&af(r,t.slice(7));i&&(i.trim&&(o=n.map(u=>ke(u)?u.trim():u)),i.number&&(o=n.map(Lc)));let a,l=r[a=go(t)]||r[a=go(Qe(t))];!l&&s&&(l=r[a=go(Jt(t))]),l&&gt(l,e,6,o);const c=r[a+"Once"];if(c){if(!e.emitted)e.emitted={};else if(e.emitted[a])return;e.emitted[a]=!0,gt(c,e,6,o)}}function pl(e,t,n=!1){const r=t.emitsCache,o=r.get(e);if(o!==void 0)return o;const s=e.emits;let i={},a=!1;if(!ie(e)){const l=c=>{const u=pl(c,t,!0);u&&(a=!0,Me(i,u))};!n&&t.mixins.length&&t.mixins.forEach(l),e.extends&&l(e.extends),e.mixins&&e.mixins.forEach(l)}return!s&&!a?(xe(e)&&r.set(e,null),null):(se(s)?s.forEach(l=>i[l]=null):Me(i,s),xe(e)&&r.set(e,i),i)}function oo(e,t){return!e||!ir(t)?!1:(t=t.slice(2).replace(/Once$/,""),ye(e,t[0].toLowerCase()+t.slice(1))||ye(e,Jt(t))||ye(e,t))}function xo(e){const{type:t,vnode:n,proxy:r,withProxy:o,propsOptions:[s],slots:i,attrs:a,emit:l,render:c,renderCache:u,props:f,data:p,setupState:g,ctx:_,inheritAttrs:b}=e,w=zr(e);let A,k;try{if(n.shapeFlag&4){const y=o||r,N=y;A=lt(c.call(N,y,u,f,g,p,_)),k=a}else{const y=t;A=lt(y.length>1?y(f,{attrs:a,slots:i,emit:l}):y(f,null)),k=t.props?a:cf(a)}}catch(y){Kn.length=0,ur(y,e,1),A=J(Be)}let m=A;if(k&&b!==!1){const y=Object.keys(k),{shapeFlag:N}=m;y.length&&N&7&&(s&&y.some(os)&&(k=uf(k,s)),m=Zt(m,k,!1,!0))}return n.dirs&&(m=Zt(m,null,!1,!0),m.dirs=m.dirs?m.dirs.concat(n.dirs):n.dirs),n.transition&&Xn(m,n.transition),A=m,zr(w),A}const cf=e=>{let t;for(const n in e)(n==="class"||n==="style"||ir(n))&&((t||(t={}))[n]=e[n]);return t},uf=(e,t)=>{const n={};for(const r in e)(!os(r)||!(r.slice(9)in t))&&(n[r]=e[r]);return n};function ff(e,t,n){const{props:r,children:o,component:s}=e,{props:i,children:a,patchFlag:l}=t,c=s.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&l>=0){if(l&1024)return!0;if(l&16)return r?ti(r,i,c):!!i;if(l&8){const u=t.dynamicProps;for(let f=0;f<u.length;f++){const p=u[f];if(i[p]!==r[p]&&!oo(c,p))return!0}}}else return(o||a)&&(!a||!a.$stable)?!0:r===i?!1:r?i?ti(r,i,c):!0:!!i;return!1}function ti(e,t,n){const r=Object.keys(t);if(r.length!==Object.keys(e).length)return!0;for(let o=0;o<r.length;o++){const s=r[o];if(t[s]!==e[s]&&!oo(n,s))return!0}return!1}function gl({vnode:e,parent:t},n){for(;t;){const r=t.subTree;if(r.suspense&&r.suspense.activeBranch===e&&(r.el=e.el),r===e)(e=t.vnode).el=n,t=t.parent;else break}}const ml=e=>e.__isSuspense;function vl(e,t){t&&t.pendingBranch?se(e)?t.effects.push(...e):t.effects.push(e):vu(e)}const ve=Symbol.for("v-fgt"),fn=Symbol.for("v-txt"),Be=Symbol.for("v-cmt"),qn=Symbol.for("v-stc"),Kn=[];let nt=null;function T(e=!1){Kn.push(nt=e?null:[])}function df(){Kn.pop(),nt=Kn[Kn.length-1]||null}let er=1;function ni(e,t=!1){er+=e,e<0&&nt&&t&&(nt.hasOnce=!0)}function _l(e){return e.dynamicChildren=er>0?nt||bn:null,df(),er>0&&nt&&nt.push(e),e}function F(e,t,n,r,o,s){return _l(j(e,t,n,r,o,s,!0))}function ge(e,t,n,r,o){return _l(J(e,t,n,r,o,!0))}function tr(e){return e?e.__v_isVNode===!0:!1}function sn(e,t){return e.type===t.type&&e.key===t.key}const yl=({key:e})=>e??null,Or=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?ke(e)||Oe(e)||ie(e)?{i:$e,r:e,k:t,f:!!n}:e:null);function j(e,t=null,n=null,r=0,o=null,s=e===ve?0:1,i=!1,a=!1){const l={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&yl(t),ref:t&&Or(t),scopeId:Ba,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:s,patchFlag:r,dynamicProps:o,dynamicChildren:null,appContext:null,ctx:$e};return a?(vs(l,n),s&128&&e.normalize(l)):n&&(l.shapeFlag|=ke(n)?8:16),er>0&&!i&&nt&&(l.patchFlag>0||s&6)&&l.patchFlag!==32&&nt.push(l),l}const J=hf;function hf(e,t=null,n=null,r=0,o=null,s=!1){if((!e||e===Za)&&(e=Be),tr(e)){const a=Zt(e,t,!0);return n&&vs(a,n),er>0&&!s&&nt&&(a.shapeFlag&6?nt[nt.indexOf(e)]=a:nt.push(a)),a.patchFlag=-2,a}if(Sf(e)&&(e=e.__vccOpts),t){t=Ir(t);let{class:a,style:l}=t;a&&!ke(a)&&(t.class=je(a)),xe(l)&&(us(l)&&!se(l)&&(l=Me({},l)),t.style=Yt(l))}const i=ke(e)?1:ml(e)?128:ja(e)?64:xe(e)?4:ie(e)?2:0;return j(e,t,n,r,o,i,s,!0)}function Ir(e){return e?us(e)||ol(e)?Me({},e):e:null}function Zt(e,t,n=!1,r=!1){const{props:o,ref:s,patchFlag:i,children:a,transition:l}=e,c=t?pf(o||{},t):o,u={__v_isVNode:!0,__v_skip:!0,type:e.type,props:c,key:c&&yl(c),ref:t&&t.ref?n&&s?se(s)?s.concat(Or(t)):[s,Or(t)]:Or(t):s,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:a,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==ve?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:l,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Zt(e.ssContent),ssFallback:e.ssFallback&&Zt(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return l&&r&&Xn(u,l.clone(u)),u}function ot(e=" ",t=0){return J(fn,null,e,t)}function so(e,t){const n=J(qn,null,e);return n.staticCount=t,n}function ue(e="",t=!1){return t?(T(),ge(Be,null,e)):J(Be,null,e)}function lt(e){return e==null||typeof e=="boolean"?J(Be):se(e)?J(ve,null,e.slice()):tr(e)?Ut(e):J(fn,null,String(e))}function Ut(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Zt(e)}function vs(e,t){let n=0;const{shapeFlag:r}=e;if(t==null)t=null;else if(se(t))n=16;else if(typeof t=="object")if(r&65){const o=t.default;o&&(o._c&&(o._d=!1),vs(e,o()),o._c&&(o._d=!0));return}else{n=32;const o=t._;!o&&!ol(t)?t._ctx=$e:o===3&&$e&&($e.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else ie(t)?(t={default:t,_ctx:$e},n=32):(t=String(t),r&64?(n=16,t=[ot(t)]):n=8);e.children=t,e.shapeFlag|=n}function pf(...e){const t={};for(let n=0;n<e.length;n++){const r=e[n];for(const o in r)if(o==="class")t.class!==r.class&&(t.class=je([t.class,r.class]));else if(o==="style")t.style=Yt([t.style,r.style]);else if(ir(o)){const s=t[o],i=r[o];i&&s!==i&&!(se(s)&&s.includes(i))&&(t[o]=s?[].concat(s,i):i)}else o!==""&&(t[o]=r[o])}return t}function at(e,t,n,r=null){gt(e,t,7,[n,r])}const gf=el();let mf=0;function vf(e,t,n){const r=e.type,o=(t?t.appContext:e.appContext)||gf,s={uid:mf++,vnode:e,type:r,parent:t,appContext:o,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Dc(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(o.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:il(r,o),emitsOptions:pl(r,o),emit:null,emitted:null,propsDefaults:Se,inheritAttrs:r.inheritAttrs,ctx:Se,data:Se,props:Se,attrs:Se,slots:Se,refs:Se,setupState:Se,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return s.ctx={_:s},s.root=t?t.root:s,s.emit=lf.bind(null,s),e.ce&&e.ce(s),s}let Re=null;const hn=()=>Re||$e;let qr,Ko;{const e=Qr(),t=(n,r)=>{let o;return(o=e[n])||(o=e[n]=[]),o.push(r),s=>{o.length>1?o.forEach(i=>i(s)):o[0](s)}};qr=t("__VUE_INSTANCE_SETTERS__",n=>Re=n),Ko=t("__VUE_SSR_SETTERS__",n=>kn=n)}const pr=e=>{const t=Re;return qr(e),e.scope.on(),()=>{e.scope.off(),qr(t)}},ri=()=>{Re&&Re.scope.off(),qr(null)};function bl(e){return e.vnode.shapeFlag&4}let kn=!1;function _f(e,t=!1,n=!1){t&&Ko(t);const{props:r,children:o}=e.vnode,s=bl(e);Ku(e,r,s,t),Yu(e,o,n||t);const i=s?yf(e,t):void 0;return t&&Ko(!1),i}function yf(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Bu);const{setup:r}=n;if(r){Ht();const o=e.setupContext=r.length>1?Ef(e):null,s=pr(e),i=cr(r,e,0,[e.props,o]),a=ia(i);if(Mt(),s(),(a||e.sp)&&!cn(e)&&ds(e),a){if(i.then(ri,ri),t)return i.then(l=>{oi(e,l)}).catch(l=>{ur(l,e,0)});e.asyncDep=i}else oi(e,i)}else El(e)}function oi(e,t,n){ie(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:xe(t)&&(e.setupState=Ra(t)),El(e)}function El(e,t,n){const r=e.type;e.render||(e.render=r.render||St);{const o=pr(e);Ht();try{ju(e)}finally{Mt(),o()}}}const bf={get(e,t){return We(e,"get",""),e[t]}};function Ef(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,bf),slots:e.slots,emit:e.emit,expose:t}}function io(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Ra(su(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in Wn)return Wn[n](e)},has(t,n){return n in t||n in Wn}})):e.proxy}function wf(e,t=!0){return ie(e)?e.displayName||e.name:e.name||t&&e.__name}function Sf(e){return ie(e)&&"__vccOpts"in e}const O=(e,t)=>du(e,t,kn);function te(e,t,n){const r=arguments.length;return r===2?xe(t)&&!se(t)?tr(t)?J(e,null,[t]):J(e,t):J(e,null,t):(r>3?n=Array.prototype.slice.call(arguments,2):r===3&&tr(n)&&(n=[n]),J(e,t,n))}const Cf="3.5.17";/**
* @vue/runtime-dom v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Go;const si=typeof window<"u"&&window.trustedTypes;if(si)try{Go=si.createPolicy("vue",{createHTML:e=>e})}catch{}const wl=Go?e=>Go.createHTML(e):e=>e,Tf="http://www.w3.org/2000/svg",xf="http://www.w3.org/1998/Math/MathML",Rt=typeof document<"u"?document:null,ii=Rt&&Rt.createElement("template"),Af={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,r)=>{const o=t==="svg"?Rt.createElementNS(Tf,e):t==="mathml"?Rt.createElementNS(xf,e):n?Rt.createElement(e,{is:n}):Rt.createElement(e);return e==="select"&&r&&r.multiple!=null&&o.setAttribute("multiple",r.multiple),o},createText:e=>Rt.createTextNode(e),createComment:e=>Rt.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Rt.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,r,o,s){const i=n?n.previousSibling:t.lastChild;if(o&&(o===s||o.nextSibling))for(;t.insertBefore(o.cloneNode(!0),n),!(o===s||!(o=o.nextSibling)););else{ii.innerHTML=wl(r==="svg"?`<svg>${e}</svg>`:r==="mathml"?`<math>${e}</math>`:e);const a=ii.content;if(r==="svg"||r==="mathml"){const l=a.firstChild;for(;l.firstChild;)a.appendChild(l.firstChild);a.removeChild(l)}t.insertBefore(a,n)}return[i?i.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},Ft="transition",Dn="animation",nr=Symbol("_vtc"),Sl={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},kf=Me({},Fa,Sl),Lf=e=>(e.displayName="Transition",e.props=kf,e),Cl=Lf((e,{slots:t})=>te(Eu,Pf(e),t)),en=(e,t=[])=>{se(e)?e.forEach(n=>n(...t)):e&&e(...t)},ai=e=>e?se(e)?e.some(t=>t.length>1):e.length>1:!1;function Pf(e){const t={};for(const E in e)E in Sl||(t[E]=e[E]);if(e.css===!1)return t;const{name:n="v",type:r,duration:o,enterFromClass:s=`${n}-enter-from`,enterActiveClass:i=`${n}-enter-active`,enterToClass:a=`${n}-enter-to`,appearFromClass:l=s,appearActiveClass:c=i,appearToClass:u=a,leaveFromClass:f=`${n}-leave-from`,leaveActiveClass:p=`${n}-leave-active`,leaveToClass:g=`${n}-leave-to`}=e,_=Rf(o),b=_&&_[0],w=_&&_[1],{onBeforeEnter:A,onEnter:k,onEnterCancelled:m,onLeave:y,onLeaveCancelled:N,onBeforeAppear:Y=A,onAppear:M=k,onAppearCancelled:S=m}=t,W=(E,H,D,X)=>{E._enterCancelled=X,tn(E,H?u:a),tn(E,H?c:i),D&&D()},I=(E,H)=>{E._isLeaving=!1,tn(E,f),tn(E,g),tn(E,p),H&&H()},Q=E=>(H,D)=>{const X=E?M:k,L=()=>W(H,E,D);en(X,[H,L]),li(()=>{tn(H,E?l:s),At(H,E?u:a),ai(X)||ci(H,r,b,L)})};return Me(t,{onBeforeEnter(E){en(A,[E]),At(E,s),At(E,i)},onBeforeAppear(E){en(Y,[E]),At(E,l),At(E,c)},onEnter:Q(!1),onAppear:Q(!0),onLeave(E,H){E._isLeaving=!0;const D=()=>I(E,H);At(E,f),E._enterCancelled?(At(E,p),di()):(di(),At(E,p)),li(()=>{E._isLeaving&&(tn(E,f),At(E,g),ai(y)||ci(E,r,w,D))}),en(y,[E,D])},onEnterCancelled(E){W(E,!1,void 0,!0),en(m,[E])},onAppearCancelled(E){W(E,!0,void 0,!0),en(S,[E])},onLeaveCancelled(E){I(E),en(N,[E])}})}function Rf(e){if(e==null)return null;if(xe(e))return[Ao(e.enter),Ao(e.leave)];{const t=Ao(e);return[t,t]}}function Ao(e){return Pc(e)}function At(e,t){t.split(/\s+/).forEach(n=>n&&e.classList.add(n)),(e[nr]||(e[nr]=new Set)).add(t)}function tn(e,t){t.split(/\s+/).forEach(r=>r&&e.classList.remove(r));const n=e[nr];n&&(n.delete(t),n.size||(e[nr]=void 0))}function li(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let $f=0;function ci(e,t,n,r){const o=e._endId=++$f,s=()=>{o===e._endId&&r()};if(n!=null)return setTimeout(s,n);const{type:i,timeout:a,propCount:l}=Of(e,t);if(!i)return r();const c=i+"end";let u=0;const f=()=>{e.removeEventListener(c,p),s()},p=g=>{g.target===e&&++u>=l&&f()};setTimeout(()=>{u<l&&f()},a+1),e.addEventListener(c,p)}function Of(e,t){const n=window.getComputedStyle(e),r=_=>(n[_]||"").split(", "),o=r(`${Ft}Delay`),s=r(`${Ft}Duration`),i=ui(o,s),a=r(`${Dn}Delay`),l=r(`${Dn}Duration`),c=ui(a,l);let u=null,f=0,p=0;t===Ft?i>0&&(u=Ft,f=i,p=s.length):t===Dn?c>0&&(u=Dn,f=c,p=l.length):(f=Math.max(i,c),u=f>0?i>c?Ft:Dn:null,p=u?u===Ft?s.length:l.length:0);const g=u===Ft&&/\b(transform|all)(,|$)/.test(r(`${Ft}Property`).toString());return{type:u,timeout:f,propCount:p,hasTransform:g}}function ui(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((n,r)=>fi(n)+fi(e[r])))}function fi(e){return e==="auto"?0:Number(e.slice(0,-1).replace(",","."))*1e3}function di(){return document.body.offsetHeight}function If(e,t,n){const r=e[nr];r&&(t=(t?[t,...r]:[...r]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const Kr=Symbol("_vod"),Tl=Symbol("_vsh"),Gr={beforeMount(e,{value:t},{transition:n}){e[Kr]=e.style.display==="none"?"":e.style.display,n&&t?n.beforeEnter(e):Nn(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:r}){!t!=!n&&(r?t?(r.beforeEnter(e),Nn(e,!0),r.enter(e)):r.leave(e,()=>{Nn(e,!1)}):Nn(e,t))},beforeUnmount(e,{value:t}){Nn(e,t)}};function Nn(e,t){e.style.display=t?e[Kr]:"none",e[Tl]=!t}const Hf=Symbol(""),Mf=/(^|;)\s*display\s*:/;function Df(e,t,n){const r=e.style,o=ke(n);let s=!1;if(n&&!o){if(t)if(ke(t))for(const i of t.split(";")){const a=i.slice(0,i.indexOf(":")).trim();n[a]==null&&Hr(r,a,"")}else for(const i in t)n[i]==null&&Hr(r,i,"");for(const i in n)i==="display"&&(s=!0),Hr(r,i,n[i])}else if(o){if(t!==n){const i=r[Hf];i&&(n+=";"+i),r.cssText=n,s=Mf.test(n)}}else t&&e.removeAttribute("style");Kr in e&&(e[Kr]=s?r.display:"",e[Tl]&&(r.display="none"))}const hi=/\s*!important$/;function Hr(e,t,n){if(se(n))n.forEach(r=>Hr(e,t,r));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const r=Nf(e,t);hi.test(n)?e.setProperty(Jt(r),n.replace(hi,""),"important"):e[r]=n}}const pi=["Webkit","Moz","ms"],ko={};function Nf(e,t){const n=ko[t];if(n)return n;let r=Qe(t);if(r!=="filter"&&r in e)return ko[t]=r;r=ar(r);for(let o=0;o<pi.length;o++){const s=pi[o]+r;if(s in e)return ko[t]=s}return t}const gi="http://www.w3.org/1999/xlink";function mi(e,t,n,r,o,s=Mc(t)){r&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(gi,t.slice(6,t.length)):e.setAttributeNS(gi,t,n):n==null||s&&!ca(n)?e.removeAttribute(t):e.setAttribute(t,s?"":Dt(n)?String(n):n)}function vi(e,t,n,r,o){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?wl(n):n);return}const s=e.tagName;if(t==="value"&&s!=="PROGRESS"&&!s.includes("-")){const a=s==="OPTION"?e.getAttribute("value")||"":e.value,l=n==null?e.type==="checkbox"?"on":"":String(n);(a!==l||!("_value"in e))&&(e.value=l),n==null&&e.removeAttribute(t),e._value=n;return}let i=!1;if(n===""||n==null){const a=typeof e[t];a==="boolean"?n=ca(n):n==null&&a==="string"?(n="",i=!0):a==="number"&&(n=0,i=!0)}try{e[t]=n}catch{}i&&e.removeAttribute(o||t)}function Bf(e,t,n,r){e.addEventListener(t,n,r)}function jf(e,t,n,r){e.removeEventListener(t,n,r)}const _i=Symbol("_vei");function Ff(e,t,n,r,o=null){const s=e[_i]||(e[_i]={}),i=s[t];if(r&&i)i.value=r;else{const[a,l]=Vf(t);if(r){const c=s[t]=Wf(r,o);Bf(e,a,c,l)}else i&&(jf(e,a,i,l),s[t]=void 0)}}const yi=/(?:Once|Passive|Capture)$/;function Vf(e){let t;if(yi.test(e)){t={};let r;for(;r=e.match(yi);)e=e.slice(0,e.length-r[0].length),t[r[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Jt(e.slice(2)),t]}let Lo=0;const zf=Promise.resolve(),Uf=()=>Lo||(zf.then(()=>Lo=0),Lo=Date.now());function Wf(e,t){const n=r=>{if(!r._vts)r._vts=Date.now();else if(r._vts<=n.attached)return;gt(qf(r,n.value),t,5,[r])};return n.value=e,n.attached=Uf(),n}function qf(e,t){if(se(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(r=>o=>!o._stopped&&r&&r(o))}else return t}const bi=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Kf=(e,t,n,r,o,s)=>{const i=o==="svg";t==="class"?If(e,r,i):t==="style"?Df(e,n,r):ir(t)?os(t)||Ff(e,t,n,r,s):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Gf(e,t,r,i))?(vi(e,t,r),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&mi(e,t,r,i,s,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!ke(r))?vi(e,Qe(t),r,s,t):(t==="true-value"?e._trueValue=r:t==="false-value"&&(e._falseValue=r),mi(e,t,r,i))};function Gf(e,t,n,r){if(r)return!!(t==="innerHTML"||t==="textContent"||t in e&&bi(t)&&ie(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const o=e.tagName;if(o==="IMG"||o==="VIDEO"||o==="CANVAS"||o==="SOURCE")return!1}return bi(t)&&ke(n)?!1:t in e}const Zf={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},Jf=(e,t)=>{const n=e._withKeys||(e._withKeys={}),r=t.join(".");return n[r]||(n[r]=o=>{if(!("key"in o))return;const s=Jt(o.key);if(t.some(i=>i===s||Zf[i]===s))return e(o)})},Yf=Me({patchProp:Kf},Af);let Po,Ei=!1;function Qf(){return Po=Ei?Po:Xu(Yf),Ei=!0,Po}const Xf=(...e)=>{const t=Qf().createApp(...e),{mount:n}=t;return t.mount=r=>{const o=td(r);if(o)return n(o,!0,ed(o))},t};function ed(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function td(e){return ke(e)?document.querySelector(e):e}var gr=e=>/^[a-z][a-z0-9+.-]*:/.test(e)||e.startsWith("//"),nd=/.md((\?|#).*)?$/,rd=(e,t="/")=>gr(e)||e.startsWith("/")&&!e.startsWith(t)&&!nd.test(e),mr=e=>/^(https?:)?\/\//.test(e),wi=e=>{if(!e||e.endsWith("/"))return e;let t=e.replace(/(^|\/)README.md$/i,"$1index.html");return t.endsWith(".md")?t=`${t.substring(0,t.length-3)}.html`:t.endsWith(".html")||(t=`${t}.html`),t.endsWith("/index.html")&&(t=t.substring(0,t.length-10)),t},od="http://.",sd=(e,t)=>{if(!e.startsWith("/")&&t){const n=t.slice(0,t.lastIndexOf("/"));return wi(new URL(`${n}/${e}`,od).pathname)}return wi(e)},id=(e,t)=>{const n=Object.keys(e).sort((r,o)=>{const s=o.split("/").length-r.split("/").length;return s!==0?s:o.length-r.length});for(const r of n)if(t.startsWith(r))return r;return"/"},ad=/(#|\?)/,xl=e=>{const[t,...n]=e.split(ad);return{pathname:t,hashAndQueries:n.join("")}},ld=["link","meta","script","style","noscript","template"],cd=["title","base"],ud=([e,t,n])=>cd.includes(e)?e:ld.includes(e)?e==="meta"&&t.name?`${e}.${t.name}`:e==="template"&&t.id?`${e}.${t.id}`:JSON.stringify([e,Object.entries(t).map(([r,o])=>typeof o=="boolean"?o?[r,""]:null:[r,o]).filter(r=>r!=null).sort(([r],[o])=>r.localeCompare(o)),n]):null,fd=e=>{const t=new Set,n=[];return e.forEach(r=>{const o=ud(r);o&&!t.has(o)&&(t.add(o),n.push(r))}),n},dd=e=>e.endsWith("/")||e.endsWith(".html")?e:`${e}/`,Al=e=>e.endsWith("/")?e.slice(0,-1):e,kl=e=>e.startsWith("/")?e.slice(1):e,_s=e=>Object.prototype.toString.call(e)==="[object Object]",pt=e=>typeof e=="string";const hd="modulepreload",pd=function(e){return"/"+e},Si={},ce=function(t,n,r){let o=Promise.resolve();if(n&&n.length>0){document.getElementsByTagName("link");const i=document.querySelector("meta[property=csp-nonce]"),a=(i==null?void 0:i.nonce)||(i==null?void 0:i.getAttribute("nonce"));o=Promise.allSettled(n.map(l=>{if(l=pd(l),l in Si)return;Si[l]=!0;const c=l.endsWith(".css"),u=c?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${l}"]${u}`))return;const f=document.createElement("link");if(f.rel=c?"stylesheet":hd,c||(f.as="script"),f.crossOrigin="",f.href=l,a&&f.setAttribute("nonce",a),document.head.appendChild(f),c)return new Promise((p,g)=>{f.addEventListener("load",p),f.addEventListener("error",()=>g(new Error(`Unable to preload CSS for ${l}`)))})}))}function s(i){const a=new Event("vite:preloadError",{cancelable:!0});if(a.payload=i,window.dispatchEvent(a),!a.defaultPrevented)throw i}return o.then(i=>{for(const a of i||[])a.status==="rejected"&&s(a.reason);return t().catch(s)})},gd=JSON.parse("{}"),md=Object.fromEntries([["/",{loader:()=>ce(()=>import("./index.html-DYRKYrUu.js"),[]),meta:{title:"双马智能科技"}}],["/get-started.html",{loader:()=>ce(()=>import("./get-started.html-5fmzPh36.js"),[]),meta:{title:"Get Started"}}],["/template-guide.html",{loader:()=>ce(()=>import("./template-guide.html-DC5-9hPJ.js"),[]),meta:{title:"双马智能科技移动端模板系统使用指南"}}],["/posts/archive1.html",{loader:()=>ce(()=>import("./archive1.html-EGkD0lgn.js"),[]),meta:{_blog:{title:"Archive Article1",author:"",date:"1998-01-01T00:00:00.000Z",category:["History"],tag:["WWI"],excerpt:`
<h2>Heading 2</h2>
<p>Here is the content.</p>
<h3>Heading 3</h3>
<p>Here is the content.</p>
`},title:"Archive Article1"}}],["/posts/archive2.html",{loader:()=>ce(()=>import("./archive2.html-oYXFxvPT.js"),[]),meta:{_blog:{title:"Archive Article2",author:"",date:"1998-01-02T00:00:00.000Z",category:["History"],tag:["WWII"],excerpt:`
<h2>Heading 2</h2>
<p>Here is the content.</p>
<h3>Heading 3</h3>
<p>Here is the content.</p>
`},title:"Archive Article2"}}],["/posts/article1.html",{loader:()=>ce(()=>import("./article1.html-DBTx-9c6.js"),[]),meta:{_blog:{title:"Article 1",author:"",date:"2022-01-01T00:00:00.000Z",category:["Category A"],tag:["tag A","tag B"],excerpt:`
<h2>Heading 2</h2>
<p>Here is the content.</p>
<h3>Heading 3</h3>
<p>Here is the content.</p>
`},title:"Article 1"}}],["/posts/article10.html",{loader:()=>ce(()=>import("./article10.html-BNqe6aIz.js"),[]),meta:{_blog:{title:"Article 10",author:"",date:"2022-01-10T00:00:00.000Z",category:["Category A","Category B"],tag:["tag C","tag D"],excerpt:`
<h2>Heading 2</h2>
<p>Here is the content.</p>
<h3>Heading 3</h3>
<p>Here is the content.</p>
`},title:"Article 10"}}],["/posts/article11.html",{loader:()=>ce(()=>import("./article11.html-CHbC7t92.js"),[]),meta:{_blog:{title:"Article 11",author:"",date:"2022-01-11T00:00:00.000Z",category:["Category A","Category B"],tag:["tag C","tag D"],excerpt:`
<h2>Heading 2</h2>
<p>Here is the content.</p>
<h3>Heading 3</h3>
<p>Here is the content.</p>
`},title:"Article 11"}}],["/posts/article12.html",{loader:()=>ce(()=>import("./article12.html-D8RWpAyh.js"),[]),meta:{_blog:{title:"Article 12",author:"",date:"2022-01-12T00:00:00.000Z",category:["Category A","Category B"],tag:["tag C","tag D"],excerpt:`
<h2>Heading 2</h2>
<p>Here is the content.</p>
<h3>Heading 3</h3>
<p>Here is the content.</p>
`},title:"Article 12"}}],["/posts/article2.html",{loader:()=>ce(()=>import("./article2.html-BUH8CJUp.js"),[]),meta:{_blog:{title:"Article 2",author:"",date:"2022-01-02T00:00:00.000Z",category:["Category A"],tag:["tag A","tag B"],excerpt:`
<h2>Heading 2</h2>
<p>Here is the content.</p>
<h3>Heading 3</h3>
<p>Here is the content.</p>
`},title:"Article 2"}}],["/posts/article3.html",{loader:()=>ce(()=>import("./article3.html-B0Hs-VEi.js"),[]),meta:{_blog:{title:"Article 3",author:"",date:"2022-01-03T00:00:00.000Z",category:["Category A","Category B"],tag:["tag A","tag B"],excerpt:`
<h2>Heading 2</h2>
<p>Here is the content.</p>
<h3>Heading 3</h3>
<p>Here is the content.</p>
`},title:"Article 3"}}],["/posts/article4.html",{loader:()=>ce(()=>import("./article4.html-C1GV-6uq.js"),[]),meta:{_blog:{title:"Article 4",author:"",date:"2022-01-04T00:00:00.000Z",category:["Category A","Category B"],tag:["tag A","tag B"],excerpt:`
<h2>Heading 2</h2>
<p>Here is the content.</p>
<h3>Heading 3</h3>
<p>Here is the content.</p>
`},title:"Article 4"}}],["/posts/article5.html",{loader:()=>ce(()=>import("./article5.html-Cl8LDaa9.js"),[]),meta:{_blog:{title:"Article 5",author:"",date:"2022-01-05T00:00:00.000Z",category:["Category A","Category B"],tag:["tag A","tag B"],excerpt:`
<h2>Heading 2</h2>
<p>Here is the content.</p>
<h3>Heading 3</h3>
<p>Here is the content.</p>
`},title:"Article 5"}}],["/posts/article6.html",{loader:()=>ce(()=>import("./article6.html-zYEzEx-4.js"),[]),meta:{_blog:{title:"Article 6",author:"",date:"2022-01-06T00:00:00.000Z",category:["Category A","Category B"],tag:["tag A","tag B"],excerpt:`
<h2>Heading 2</h2>
<p>Here is the content.</p>
<h3>Heading 3</h3>
<p>Here is the content.</p>
`},title:"Article 6"}}],["/posts/article7.html",{loader:()=>ce(()=>import("./article7.html-dlC0uYkU.js"),[]),meta:{_blog:{title:"Article 7",author:"",date:"2022-01-07T00:00:00.000Z",category:["Category A","Category B"],tag:["tag C","tag D"],excerpt:`
<h2>Heading 2</h2>
<p>Here is the content.</p>
<h3>Heading 3</h3>
<p>Here is the content.</p>
`},title:"Article 7"}}],["/posts/article8.html",{loader:()=>ce(()=>import("./article8.html-VyvD1kBA.js"),[]),meta:{_blog:{title:"Article 8",author:"",date:"2022-01-08T00:00:00.000Z",category:["Category A","Category B"],tag:["tag C","tag D"],excerpt:`
<h2>Heading 2</h2>
<p>Here is the content.</p>
<h3>Heading 3</h3>
<p>Here is the content.</p>
`},title:"Article 8"}}],["/posts/article9.html",{loader:()=>ce(()=>import("./article9.html-Bhp04mQM.js"),[]),meta:{_blog:{title:"Article 9",author:"",date:"2022-01-09T00:00:00.000Z",category:["Category A","Category B"],tag:["tag C","tag D"],excerpt:`
<h2>Heading 2</h2>
<p>Here is the content.</p>
<h3>Heading 3</h3>
<p>Here is the content.</p>
`},title:"Article 9"}}],["/posts/sticky.html",{loader:()=>ce(()=>import("./sticky.html-gaTBN3yp.js"),[]),meta:{_blog:{title:"Sticky Article",author:"",date:"2021-01-01T00:00:00.000Z",category:["Category C"],tag:["tag E"],excerpt:"<p>A sticky article demo.</p>"},title:"Sticky Article"}}],["/posts/sticky2.html",{loader:()=>ce(()=>import("./sticky2.html-9-vGNyok.js"),[]),meta:{_blog:{title:"Sticky Article with Higher Priority",author:"",date:"2020-01-01T00:00:00.000Z",category:["Category C"],tag:["tag E"],excerpt:`
<p>Excerpt information which is added manually.</p>
`},title:"Sticky Article with Higher Priority"}}],["/products/",{loader:()=>ce(()=>import("./index.html-NvQirkLZ.js"),[]),meta:{title:"产品展示"}}],["/qualification/",{loader:()=>ce(()=>import("./index.html-C5pJJnaS.js"),[]),meta:{title:"资质证书"}}],["/company/",{loader:()=>ce(()=>import("./index.html-BN79YOQD.js"),[]),meta:{title:"公司简介/Company Profile"}}],["/workshop/",{loader:()=>ce(()=>import("./index.html-ByRMJ_oG.js"),[]),meta:{title:"生产车间"}}],["/404.html",{loader:()=>ce(()=>import("./404.html-likX985B.js"),[]),meta:{title:""}}],["/category/",{loader:()=>ce(()=>import("./index.html-TCOrlirZ.js"),[]),meta:{title:"Categories"}}],["/category/history/",{loader:()=>ce(()=>import("./index.html-DVX6qurA.js"),[]),meta:{title:"Category History"}}],["/category/category-a/",{loader:()=>ce(()=>import("./index.html-XlhBzKgp.js"),[]),meta:{title:"Category Category A"}}],["/category/category-b/",{loader:()=>ce(()=>import("./index.html-CtQdTlkD.js"),[]),meta:{title:"Category Category B"}}],["/category/category-c/",{loader:()=>ce(()=>import("./index.html-Y8HuFjKm.js"),[]),meta:{title:"Category Category C"}}],["/tag/",{loader:()=>ce(()=>import("./index.html-DgivAJSH.js"),[]),meta:{title:"Tags"}}],["/tag/wwi/",{loader:()=>ce(()=>import("./index.html-CTRpxP7d.js"),[]),meta:{title:"Tag WWI"}}],["/tag/wwii/",{loader:()=>ce(()=>import("./index.html-CTWWppU7.js"),[]),meta:{title:"Tag WWII"}}],["/tag/tag-a/",{loader:()=>ce(()=>import("./index.html-CfWqudIE.js"),[]),meta:{title:"Tag tag A"}}],["/tag/tag-b/",{loader:()=>ce(()=>import("./index.html-UHr6Utdl.js"),[]),meta:{title:"Tag tag B"}}],["/tag/tag-c/",{loader:()=>ce(()=>import("./index.html-Bs57N6Fz.js"),[]),meta:{title:"Tag tag C"}}],["/tag/tag-d/",{loader:()=>ce(()=>import("./index.html-DtvD1dui.js"),[]),meta:{title:"Tag tag D"}}],["/tag/tag-e/",{loader:()=>ce(()=>import("./index.html-dM_CHu3f.js"),[]),meta:{title:"Tag tag E"}}],["/article/",{loader:()=>ce(()=>import("./index.html-H2Km9NVt.js"),[]),meta:{title:"Articles"}}],["/timeline/",{loader:()=>ce(()=>import("./index.html-BCvwTnP1.js"),[]),meta:{title:"Timeline"}}]]);/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const _n=typeof document<"u";function Ll(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function vd(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&Ll(e.default)}const _e=Object.assign;function Ro(e,t){const n={};for(const r in t){const o=t[r];n[r]=mt(o)?o.map(e):e(o)}return n}const Gn=()=>{},mt=Array.isArray,Pl=/#/g,_d=/&/g,yd=/\//g,bd=/=/g,Ed=/\?/g,Rl=/\+/g,wd=/%5B/g,Sd=/%5D/g,$l=/%5E/g,Cd=/%60/g,Ol=/%7B/g,Td=/%7C/g,Il=/%7D/g,xd=/%20/g;function ys(e){return encodeURI(""+e).replace(Td,"|").replace(wd,"[").replace(Sd,"]")}function Ad(e){return ys(e).replace(Ol,"{").replace(Il,"}").replace($l,"^")}function Zo(e){return ys(e).replace(Rl,"%2B").replace(xd,"+").replace(Pl,"%23").replace(_d,"%26").replace(Cd,"`").replace(Ol,"{").replace(Il,"}").replace($l,"^")}function kd(e){return Zo(e).replace(bd,"%3D")}function Ld(e){return ys(e).replace(Pl,"%23").replace(Ed,"%3F")}function Pd(e){return e==null?"":Ld(e).replace(yd,"%2F")}function rr(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const Rd=/\/$/,$d=e=>e.replace(Rd,"");function $o(e,t,n="/"){let r,o={},s="",i="";const a=t.indexOf("#");let l=t.indexOf("?");return a<l&&a>=0&&(l=-1),l>-1&&(r=t.slice(0,l),s=t.slice(l+1,a>-1?a:t.length),o=e(s)),a>-1&&(r=r||t.slice(0,a),i=t.slice(a,t.length)),r=Md(r??t,n),{fullPath:r+(s&&"?")+s+i,path:r,query:o,hash:rr(i)}}function Od(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function Ci(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Id(e,t,n){const r=t.matched.length-1,o=n.matched.length-1;return r>-1&&r===o&&Ln(t.matched[r],n.matched[o])&&Hl(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function Ln(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Hl(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!Hd(e[n],t[n]))return!1;return!0}function Hd(e,t){return mt(e)?Ti(e,t):mt(t)?Ti(t,e):e===t}function Ti(e,t){return mt(t)?e.length===t.length&&e.every((n,r)=>n===t[r]):e.length===1&&e[0]===t}function Md(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),r=e.split("/"),o=r[r.length-1];(o===".."||o===".")&&r.push("");let s=n.length-1,i,a;for(i=0;i<r.length;i++)if(a=r[i],a!==".")if(a==="..")s>1&&s--;else break;return n.slice(0,s).join("/")+"/"+r.slice(i).join("/")}const Pt={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var or;(function(e){e.pop="pop",e.push="push"})(or||(or={}));var Zn;(function(e){e.back="back",e.forward="forward",e.unknown=""})(Zn||(Zn={}));function Dd(e){if(!e)if(_n){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),$d(e)}const Nd=/^[^#]+#/;function Bd(e,t){return e.replace(Nd,"#")+t}function jd(e,t){const n=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:t.behavior,left:r.left-n.left-(t.left||0),top:r.top-n.top-(t.top||0)}}const ao=()=>({left:window.scrollX,top:window.scrollY});function Fd(e){let t;if("el"in e){const n=e.el,r=typeof n=="string"&&n.startsWith("#"),o=typeof n=="string"?r?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!o)return;t=jd(o,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function xi(e,t){return(history.state?history.state.position-t:-1)+e}const Jo=new Map;function Vd(e,t){Jo.set(e,t)}function zd(e){const t=Jo.get(e);return Jo.delete(e),t}let Ud=()=>location.protocol+"//"+location.host;function Ml(e,t){const{pathname:n,search:r,hash:o}=t,s=e.indexOf("#");if(s>-1){let a=o.includes(e.slice(s))?e.slice(s).length:1,l=o.slice(a);return l[0]!=="/"&&(l="/"+l),Ci(l,"")}return Ci(n,e)+r+o}function Wd(e,t,n,r){let o=[],s=[],i=null;const a=({state:p})=>{const g=Ml(e,location),_=n.value,b=t.value;let w=0;if(p){if(n.value=g,t.value=p,i&&i===_){i=null;return}w=b?p.position-b.position:0}else r(g);o.forEach(A=>{A(n.value,_,{delta:w,type:or.pop,direction:w?w>0?Zn.forward:Zn.back:Zn.unknown})})};function l(){i=n.value}function c(p){o.push(p);const g=()=>{const _=o.indexOf(p);_>-1&&o.splice(_,1)};return s.push(g),g}function u(){const{history:p}=window;p.state&&p.replaceState(_e({},p.state,{scroll:ao()}),"")}function f(){for(const p of s)p();s=[],window.removeEventListener("popstate",a),window.removeEventListener("beforeunload",u)}return window.addEventListener("popstate",a),window.addEventListener("beforeunload",u,{passive:!0}),{pauseListeners:l,listen:c,destroy:f}}function Ai(e,t,n,r=!1,o=!1){return{back:e,current:t,forward:n,replaced:r,position:window.history.length,scroll:o?ao():null}}function qd(e){const{history:t,location:n}=window,r={value:Ml(e,n)},o={value:t.state};o.value||s(r.value,{back:null,current:r.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function s(l,c,u){const f=e.indexOf("#"),p=f>-1?(n.host&&document.querySelector("base")?e:e.slice(f))+l:Ud()+e+l;try{t[u?"replaceState":"pushState"](c,"",p),o.value=c}catch(g){console.error(g),n[u?"replace":"assign"](p)}}function i(l,c){const u=_e({},t.state,Ai(o.value.back,l,o.value.forward,!0),c,{position:o.value.position});s(l,u,!0),r.value=l}function a(l,c){const u=_e({},o.value,t.state,{forward:l,scroll:ao()});s(u.current,u,!0);const f=_e({},Ai(r.value,l,null),{position:u.position+1},c);s(l,f,!1),r.value=l}return{location:r,state:o,push:a,replace:i}}function Kd(e){e=Dd(e);const t=qd(e),n=Wd(e,t.state,t.location,t.replace);function r(s,i=!0){i||n.pauseListeners(),history.go(s)}const o=_e({location:"",base:e,go:r,createHref:Bd.bind(null,e)},t,n);return Object.defineProperty(o,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(o,"state",{enumerable:!0,get:()=>t.state.value}),o}function Gd(e){return typeof e=="string"||e&&typeof e=="object"}function Dl(e){return typeof e=="string"||typeof e=="symbol"}const Nl=Symbol("");var ki;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(ki||(ki={}));function Pn(e,t){return _e(new Error,{type:e,[Nl]:!0},t)}function kt(e,t){return e instanceof Error&&Nl in e&&(t==null||!!(e.type&t))}const Li="[^/]+?",Zd={sensitive:!1,strict:!1,start:!0,end:!0},Jd=/[.+*?^${}()[\]/\\]/g;function Yd(e,t){const n=_e({},Zd,t),r=[];let o=n.start?"^":"";const s=[];for(const c of e){const u=c.length?[]:[90];n.strict&&!c.length&&(o+="/");for(let f=0;f<c.length;f++){const p=c[f];let g=40+(n.sensitive?.25:0);if(p.type===0)f||(o+="/"),o+=p.value.replace(Jd,"\\$&"),g+=40;else if(p.type===1){const{value:_,repeatable:b,optional:w,regexp:A}=p;s.push({name:_,repeatable:b,optional:w});const k=A||Li;if(k!==Li){g+=10;try{new RegExp(`(${k})`)}catch(y){throw new Error(`Invalid custom RegExp for param "${_}" (${k}): `+y.message)}}let m=b?`((?:${k})(?:/(?:${k}))*)`:`(${k})`;f||(m=w&&c.length<2?`(?:/${m})`:"/"+m),w&&(m+="?"),o+=m,g+=20,w&&(g+=-8),b&&(g+=-20),k===".*"&&(g+=-50)}u.push(g)}r.push(u)}if(n.strict&&n.end){const c=r.length-1;r[c][r[c].length-1]+=.7000000000000001}n.strict||(o+="/?"),n.end?o+="$":n.strict&&!o.endsWith("/")&&(o+="(?:/|$)");const i=new RegExp(o,n.sensitive?"":"i");function a(c){const u=c.match(i),f={};if(!u)return null;for(let p=1;p<u.length;p++){const g=u[p]||"",_=s[p-1];f[_.name]=g&&_.repeatable?g.split("/"):g}return f}function l(c){let u="",f=!1;for(const p of e){(!f||!u.endsWith("/"))&&(u+="/"),f=!1;for(const g of p)if(g.type===0)u+=g.value;else if(g.type===1){const{value:_,repeatable:b,optional:w}=g,A=_ in c?c[_]:"";if(mt(A)&&!b)throw new Error(`Provided param "${_}" is an array but it is not repeatable (* or + modifiers)`);const k=mt(A)?A.join("/"):A;if(!k)if(w)p.length<2&&(u.endsWith("/")?u=u.slice(0,-1):f=!0);else throw new Error(`Missing required param "${_}"`);u+=k}}return u||"/"}return{re:i,score:r,keys:s,parse:a,stringify:l}}function Qd(e,t){let n=0;for(;n<e.length&&n<t.length;){const r=t[n]-e[n];if(r)return r;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function Bl(e,t){let n=0;const r=e.score,o=t.score;for(;n<r.length&&n<o.length;){const s=Qd(r[n],o[n]);if(s)return s;n++}if(Math.abs(o.length-r.length)===1){if(Pi(r))return 1;if(Pi(o))return-1}return o.length-r.length}function Pi(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Xd={type:0,value:""},eh=/[a-zA-Z0-9_]/;function th(e){if(!e)return[[]];if(e==="/")return[[Xd]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(g){throw new Error(`ERR (${n})/"${c}": ${g}`)}let n=0,r=n;const o=[];let s;function i(){s&&o.push(s),s=[]}let a=0,l,c="",u="";function f(){c&&(n===0?s.push({type:0,value:c}):n===1||n===2||n===3?(s.length>1&&(l==="*"||l==="+")&&t(`A repeatable param (${c}) must be alone in its segment. eg: '/:ids+.`),s.push({type:1,value:c,regexp:u,repeatable:l==="*"||l==="+",optional:l==="*"||l==="?"})):t("Invalid state to consume buffer"),c="")}function p(){c+=l}for(;a<e.length;){if(l=e[a++],l==="\\"&&n!==2){r=n,n=4;continue}switch(n){case 0:l==="/"?(c&&f(),i()):l===":"?(f(),n=1):p();break;case 4:p(),n=r;break;case 1:l==="("?n=2:eh.test(l)?p():(f(),n=0,l!=="*"&&l!=="?"&&l!=="+"&&a--);break;case 2:l===")"?u[u.length-1]=="\\"?u=u.slice(0,-1)+l:n=3:u+=l;break;case 3:f(),n=0,l!=="*"&&l!=="?"&&l!=="+"&&a--,u="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${c}"`),f(),i(),o}function nh(e,t,n){const r=Yd(th(e.path),n),o=_e(r,{record:e,parent:t,children:[],alias:[]});return t&&!o.record.aliasOf==!t.record.aliasOf&&t.children.push(o),o}function rh(e,t){const n=[],r=new Map;t=Ii({strict:!1,end:!0,sensitive:!1},t);function o(f){return r.get(f)}function s(f,p,g){const _=!g,b=$i(f);b.aliasOf=g&&g.record;const w=Ii(t,f),A=[b];if("alias"in f){const y=typeof f.alias=="string"?[f.alias]:f.alias;for(const N of y)A.push($i(_e({},b,{components:g?g.record.components:b.components,path:N,aliasOf:g?g.record:b})))}let k,m;for(const y of A){const{path:N}=y;if(p&&N[0]!=="/"){const Y=p.record.path,M=Y[Y.length-1]==="/"?"":"/";y.path=p.record.path+(N&&M+N)}if(k=nh(y,p,w),g?g.alias.push(k):(m=m||k,m!==k&&m.alias.push(k),_&&f.name&&!Oi(k)&&i(f.name)),jl(k)&&l(k),b.children){const Y=b.children;for(let M=0;M<Y.length;M++)s(Y[M],k,g&&g.children[M])}g=g||k}return m?()=>{i(m)}:Gn}function i(f){if(Dl(f)){const p=r.get(f);p&&(r.delete(f),n.splice(n.indexOf(p),1),p.children.forEach(i),p.alias.forEach(i))}else{const p=n.indexOf(f);p>-1&&(n.splice(p,1),f.record.name&&r.delete(f.record.name),f.children.forEach(i),f.alias.forEach(i))}}function a(){return n}function l(f){const p=ih(f,n);n.splice(p,0,f),f.record.name&&!Oi(f)&&r.set(f.record.name,f)}function c(f,p){let g,_={},b,w;if("name"in f&&f.name){if(g=r.get(f.name),!g)throw Pn(1,{location:f});w=g.record.name,_=_e(Ri(p.params,g.keys.filter(m=>!m.optional).concat(g.parent?g.parent.keys.filter(m=>m.optional):[]).map(m=>m.name)),f.params&&Ri(f.params,g.keys.map(m=>m.name))),b=g.stringify(_)}else if(f.path!=null)b=f.path,g=n.find(m=>m.re.test(b)),g&&(_=g.parse(b),w=g.record.name);else{if(g=p.name?r.get(p.name):n.find(m=>m.re.test(p.path)),!g)throw Pn(1,{location:f,currentLocation:p});w=g.record.name,_=_e({},p.params,f.params),b=g.stringify(_)}const A=[];let k=g;for(;k;)A.unshift(k.record),k=k.parent;return{name:w,path:b,params:_,matched:A,meta:sh(A)}}e.forEach(f=>s(f));function u(){n.length=0,r.clear()}return{addRoute:s,resolve:c,removeRoute:i,clearRoutes:u,getRoutes:a,getRecordMatcher:o}}function Ri(e,t){const n={};for(const r of t)r in e&&(n[r]=e[r]);return n}function $i(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:oh(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function oh(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const r in e.components)t[r]=typeof n=="object"?n[r]:n;return t}function Oi(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function sh(e){return e.reduce((t,n)=>_e(t,n.meta),{})}function Ii(e,t){const n={};for(const r in e)n[r]=r in t?t[r]:e[r];return n}function ih(e,t){let n=0,r=t.length;for(;n!==r;){const s=n+r>>1;Bl(e,t[s])<0?r=s:n=s+1}const o=ah(e);return o&&(r=t.lastIndexOf(o,r-1)),r}function ah(e){let t=e;for(;t=t.parent;)if(jl(t)&&Bl(e,t)===0)return t}function jl({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function lh(e){const t={};if(e===""||e==="?")return t;const r=(e[0]==="?"?e.slice(1):e).split("&");for(let o=0;o<r.length;++o){const s=r[o].replace(Rl," "),i=s.indexOf("="),a=rr(i<0?s:s.slice(0,i)),l=i<0?null:rr(s.slice(i+1));if(a in t){let c=t[a];mt(c)||(c=t[a]=[c]),c.push(l)}else t[a]=l}return t}function Hi(e){let t="";for(let n in e){const r=e[n];if(n=kd(n),r==null){r!==void 0&&(t+=(t.length?"&":"")+n);continue}(mt(r)?r.map(s=>s&&Zo(s)):[r&&Zo(r)]).forEach(s=>{s!==void 0&&(t+=(t.length?"&":"")+n,s!=null&&(t+="="+s))})}return t}function ch(e){const t={};for(const n in e){const r=e[n];r!==void 0&&(t[n]=mt(r)?r.map(o=>o==null?null:""+o):r==null?r:""+r)}return t}const uh=Symbol(""),Mi=Symbol(""),lo=Symbol(""),bs=Symbol(""),Yo=Symbol("");function Bn(){let e=[];function t(r){return e.push(r),()=>{const o=e.indexOf(r);o>-1&&e.splice(o,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function Wt(e,t,n,r,o,s=i=>i()){const i=r&&(r.enterCallbacks[o]=r.enterCallbacks[o]||[]);return()=>new Promise((a,l)=>{const c=p=>{p===!1?l(Pn(4,{from:n,to:t})):p instanceof Error?l(p):Gd(p)?l(Pn(2,{from:t,to:p})):(i&&r.enterCallbacks[o]===i&&typeof p=="function"&&i.push(p),a())},u=s(()=>e.call(r&&r.instances[o],t,n,c));let f=Promise.resolve(u);e.length<3&&(f=f.then(c)),f.catch(p=>l(p))})}function Oo(e,t,n,r,o=s=>s()){const s=[];for(const i of e)for(const a in i.components){let l=i.components[a];if(!(t!=="beforeRouteEnter"&&!i.instances[a]))if(Ll(l)){const u=(l.__vccOpts||l)[t];u&&s.push(Wt(u,n,r,i,a,o))}else{let c=l();s.push(()=>c.then(u=>{if(!u)throw new Error(`Couldn't resolve component "${a}" at "${i.path}"`);const f=vd(u)?u.default:u;i.mods[a]=u,i.components[a]=f;const g=(f.__vccOpts||f)[t];return g&&Wt(g,n,r,i,a,o)()}))}}return s}function Di(e){const t=Fe(lo),n=Fe(bs),r=O(()=>{const l=B(e.to);return t.resolve(l)}),o=O(()=>{const{matched:l}=r.value,{length:c}=l,u=l[c-1],f=n.matched;if(!u||!f.length)return-1;const p=f.findIndex(Ln.bind(null,u));if(p>-1)return p;const g=Ni(l[c-2]);return c>1&&Ni(u)===g&&f[f.length-1].path!==g?f.findIndex(Ln.bind(null,l[c-2])):p}),s=O(()=>o.value>-1&&gh(n.params,r.value.params)),i=O(()=>o.value>-1&&o.value===n.matched.length-1&&Hl(n.params,r.value.params));function a(l={}){if(ph(l)){const c=t[B(e.replace)?"replace":"push"](B(e.to)).catch(Gn);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>c),c}return Promise.resolve()}return{route:r,href:O(()=>r.value.href),isActive:s,isExactActive:i,navigate:a}}function fh(e){return e.length===1?e[0]:e}const dh=me({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:Di,setup(e,{slots:t}){const n=lr(Di(e)),{options:r}=Fe(lo),o=O(()=>({[Bi(e.activeClass,r.linkActiveClass,"router-link-active")]:n.isActive,[Bi(e.exactActiveClass,r.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const s=t.default&&fh(t.default(n));return e.custom?s:te("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:o.value},s)}}}),hh=dh;function ph(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function gh(e,t){for(const n in t){const r=t[n],o=e[n];if(typeof r=="string"){if(r!==o)return!1}else if(!mt(o)||o.length!==r.length||r.some((s,i)=>s!==o[i]))return!1}return!0}function Ni(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Bi=(e,t,n)=>e??t??n,mh=me({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const r=Fe(Yo),o=O(()=>e.route||r.value),s=Fe(Mi,0),i=O(()=>{let c=B(s);const{matched:u}=o.value;let f;for(;(f=u[c])&&!f.components;)c++;return c}),a=O(()=>o.value.matched[i.value]);Kt(Mi,O(()=>i.value+1)),Kt(uh,a),Kt(Yo,o);const l=qe();return ut(()=>[l.value,a.value,e.name],([c,u,f],[p,g,_])=>{u&&(u.instances[f]=c,g&&g!==u&&c&&c===p&&(u.leaveGuards.size||(u.leaveGuards=g.leaveGuards),u.updateGuards.size||(u.updateGuards=g.updateGuards))),c&&u&&(!g||!Ln(u,g)||!p)&&(u.enterCallbacks[f]||[]).forEach(b=>b(c))},{flush:"post"}),()=>{const c=o.value,u=e.name,f=a.value,p=f&&f.components[u];if(!p)return ji(n.default,{Component:p,route:c});const g=f.props[u],_=g?g===!0?c.params:typeof g=="function"?g(c):g:null,w=te(p,_e({},_,t,{onVnodeUnmounted:A=>{A.component.isUnmounted&&(f.instances[u]=null)},ref:l}));return ji(n.default,{Component:w,route:c})||w}}});function ji(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const vh=mh;function _h(e){const t=rh(e.routes,e),n=e.parseQuery||lh,r=e.stringifyQuery||Hi,o=e.history,s=Bn(),i=Bn(),a=Bn(),l=Pe(Pt);let c=Pt;_n&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const u=Ro.bind(null,x=>""+x),f=Ro.bind(null,Pd),p=Ro.bind(null,rr);function g(x,Z){let U,ee;return Dl(x)?(U=t.getRecordMatcher(x),ee=Z):ee=x,t.addRoute(ee,U)}function _(x){const Z=t.getRecordMatcher(x);Z&&t.removeRoute(Z)}function b(){return t.getRoutes().map(x=>x.record)}function w(x){return!!t.getRecordMatcher(x)}function A(x,Z){if(Z=_e({},Z||l.value),typeof x=="string"){const h=$o(n,x,Z.path),v=t.resolve({path:h.path},Z),P=o.createHref(h.fullPath);return _e(h,v,{params:p(v.params),hash:rr(h.hash),redirectedFrom:void 0,href:P})}let U;if(x.path!=null)U=_e({},x,{path:$o(n,x.path,Z.path).path});else{const h=_e({},x.params);for(const v in h)h[v]==null&&delete h[v];U=_e({},x,{params:f(h)}),Z.params=f(Z.params)}const ee=t.resolve(U,Z),fe=x.hash||"";ee.params=u(p(ee.params));const we=Od(r,_e({},x,{hash:Ad(fe),path:ee.path})),d=o.createHref(we);return _e({fullPath:we,hash:fe,query:r===Hi?ch(x.query):x.query||{}},ee,{redirectedFrom:void 0,href:d})}function k(x){return typeof x=="string"?$o(n,x,l.value.path):_e({},x)}function m(x,Z){if(c!==x)return Pn(8,{from:Z,to:x})}function y(x){return M(x)}function N(x){return y(_e(k(x),{replace:!0}))}function Y(x){const Z=x.matched[x.matched.length-1];if(Z&&Z.redirect){const{redirect:U}=Z;let ee=typeof U=="function"?U(x):U;return typeof ee=="string"&&(ee=ee.includes("?")||ee.includes("#")?ee=k(ee):{path:ee},ee.params={}),_e({query:x.query,hash:x.hash,params:ee.path!=null?{}:x.params},ee)}}function M(x,Z){const U=c=A(x),ee=l.value,fe=x.state,we=x.force,d=x.replace===!0,h=Y(U);if(h)return M(_e(k(h),{state:typeof h=="object"?_e({},fe,h.state):fe,force:we,replace:d}),Z||U);const v=U;v.redirectedFrom=Z;let P;return!we&&Id(r,ee,U)&&(P=Pn(16,{to:v,from:ee}),Ze(ee,ee,!0,!1)),(P?Promise.resolve(P):I(v,ee)).catch(C=>kt(C)?kt(C,2)?C:De(C):K(C,v,ee)).then(C=>{if(C){if(kt(C,2))return M(_e({replace:d},k(C.to),{state:typeof C.to=="object"?_e({},fe,C.to.state):fe,force:we}),Z||v)}else C=E(v,ee,!0,d,fe);return Q(v,ee,C),C})}function S(x,Z){const U=m(x,Z);return U?Promise.reject(U):Promise.resolve()}function W(x){const Z=jt.values().next().value;return Z&&typeof Z.runWithContext=="function"?Z.runWithContext(x):x()}function I(x,Z){let U;const[ee,fe,we]=yh(x,Z);U=Oo(ee.reverse(),"beforeRouteLeave",x,Z);for(const h of ee)h.leaveGuards.forEach(v=>{U.push(Wt(v,x,Z))});const d=S.bind(null,x,Z);return U.push(d),Je(U).then(()=>{U=[];for(const h of s.list())U.push(Wt(h,x,Z));return U.push(d),Je(U)}).then(()=>{U=Oo(fe,"beforeRouteUpdate",x,Z);for(const h of fe)h.updateGuards.forEach(v=>{U.push(Wt(v,x,Z))});return U.push(d),Je(U)}).then(()=>{U=[];for(const h of we)if(h.beforeEnter)if(mt(h.beforeEnter))for(const v of h.beforeEnter)U.push(Wt(v,x,Z));else U.push(Wt(h.beforeEnter,x,Z));return U.push(d),Je(U)}).then(()=>(x.matched.forEach(h=>h.enterCallbacks={}),U=Oo(we,"beforeRouteEnter",x,Z,W),U.push(d),Je(U))).then(()=>{U=[];for(const h of i.list())U.push(Wt(h,x,Z));return U.push(d),Je(U)}).catch(h=>kt(h,8)?h:Promise.reject(h))}function Q(x,Z,U){a.list().forEach(ee=>W(()=>ee(x,Z,U)))}function E(x,Z,U,ee,fe){const we=m(x,Z);if(we)return we;const d=Z===Pt,h=_n?history.state:{};U&&(ee||d?o.replace(x.fullPath,_e({scroll:d&&h&&h.scroll},fe)):o.push(x.fullPath,fe)),l.value=x,Ze(x,Z,U,d),De()}let H;function D(){H||(H=o.listen((x,Z,U)=>{if(!_t.listening)return;const ee=A(x),fe=Y(ee);if(fe){M(_e(fe,{replace:!0,force:!0}),ee).catch(Gn);return}c=ee;const we=l.value;_n&&Vd(xi(we.fullPath,U.delta),ao()),I(ee,we).catch(d=>kt(d,12)?d:kt(d,2)?(M(_e(k(d.to),{force:!0}),ee).then(h=>{kt(h,20)&&!U.delta&&U.type===or.pop&&o.go(-1,!1)}).catch(Gn),Promise.reject()):(U.delta&&o.go(-U.delta,!1),K(d,ee,we))).then(d=>{d=d||E(ee,we,!1),d&&(U.delta&&!kt(d,8)?o.go(-U.delta,!1):U.type===or.pop&&kt(d,20)&&o.go(-1,!1)),Q(ee,we,d)}).catch(Gn)}))}let X=Bn(),L=Bn(),re;function K(x,Z,U){De(x);const ee=L.list();return ee.length?ee.forEach(fe=>fe(x,Z,U)):console.error(x),Promise.reject(x)}function be(){return re&&l.value!==Pt?Promise.resolve():new Promise((x,Z)=>{X.add([x,Z])})}function De(x){return re||(re=!x,D(),X.list().forEach(([Z,U])=>x?U(x):Z()),X.reset()),x}function Ze(x,Z,U,ee){const{scrollBehavior:fe}=e;if(!_n||!fe)return Promise.resolve();const we=!U&&zd(xi(x.fullPath,0))||(ee||!U)&&history.state&&history.state.scroll||null;return fr().then(()=>fe(x,Z,we)).then(d=>d&&Fd(d)).catch(d=>K(d,x,Z))}const Ve=x=>o.go(x);let Bt;const jt=new Set,_t={currentRoute:l,listening:!0,addRoute:g,removeRoute:_,clearRoutes:t.clearRoutes,hasRoute:w,getRoutes:b,resolve:A,options:e,push:y,replace:N,go:Ve,back:()=>Ve(-1),forward:()=>Ve(1),beforeEach:s.add,beforeResolve:i.add,afterEach:a.add,onError:L.add,isReady:be,install(x){const Z=this;x.component("RouterLink",hh),x.component("RouterView",vh),x.config.globalProperties.$router=Z,Object.defineProperty(x.config.globalProperties,"$route",{enumerable:!0,get:()=>B(l)}),_n&&!Bt&&l.value===Pt&&(Bt=!0,y(o.location).catch(fe=>{}));const U={};for(const fe in Pt)Object.defineProperty(U,fe,{get:()=>l.value[fe],enumerable:!0});x.provide(lo,Z),x.provide(bs,La(U)),x.provide(Yo,l);const ee=x.unmount;jt.add(x),x.unmount=function(){jt.delete(x),jt.size<1&&(c=Pt,H&&H(),H=null,l.value=Pt,Bt=!1,re=!1),ee()}}};function Je(x){return x.reduce((Z,U)=>Z.then(()=>W(U)),Promise.resolve())}return _t}function yh(e,t){const n=[],r=[],o=[],s=Math.max(t.matched.length,e.matched.length);for(let i=0;i<s;i++){const a=t.matched[i];a&&(e.matched.find(c=>Ln(c,a))?r.push(a):n.push(a));const l=e.matched[i];l&&(t.matched.find(c=>Ln(c,l))||o.push(l))}return[n,r,o]}function vr(){return Fe(lo)}function On(e){return Fe(bs)}var Es=Symbol(""),vt=()=>{const e=Fe(Es);if(!e)throw new Error("useClientData() is called without provider.");return e},bh=()=>vt().pageComponent,co=()=>vt().pageData,In=()=>vt().pageFrontmatter,Eh=()=>vt().pageHead,ws=()=>vt().pageLang,wh=()=>vt().pageLayout,Ss=()=>vt().routeLocale,uo=()=>vt().routePath,Sh=()=>vt().routes,Ch=()=>vt().siteData,Mr=Pe([]),_r=e=>{Mr.value.push(e),hr(()=>{Mr.value=Mr.value.filter(t=>t!==e)})},Th=Symbol(""),Qo=Pe(gd),Tn=Pe(md),Fl=(e,t)=>{const n=sd(e,t);if(Tn.value[n])return n;const r=encodeURI(n);if(Tn.value[r])return r;const o=Qo.value[n]||Qo.value[r];return o||n},dn=(e,t)=>{const{pathname:n,hashAndQueries:r}=xl(e),o=Fl(n,t),s=o+r;return Tn.value[o]?{...Tn.value[o],path:s,notFound:!1}:{...Tn.value["/404.html"],path:s,notFound:!0}},xh=(e,t)=>{const{pathname:n,hashAndQueries:r}=xl(e);return Fl(n,t)+r},Ah=e=>{if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget){const t=e.currentTarget.getAttribute("target");if(t!=null&&t.match(/\b_blank\b/i))return}return e.preventDefault(),!0}},Ct=me({name:"RouteLink",props:{to:{type:String,required:!0},active:Boolean,activeClass:{type:String,default:"route-link-active"}},slots:Object,setup(e,{slots:t}){const n=vr(),r=On(),o=O(()=>e.to.startsWith("#")||e.to.startsWith("?")?e.to:`/${xh(e.to,r.path).substring(1)}`);return()=>te("a",{class:["route-link",{[e.activeClass]:e.active}],href:o.value,onClick:(s={})=>{Ah(s)&&n.push(e.to).catch()}},t.default())}}),kh=me({name:"AutoLink",props:{config:{type:Object,required:!0}},slots:Object,setup(e,{slots:t}){const n=Ia(e,"config"),r=On(),o=Ch(),s=O(()=>gr(n.value.link)),i=O(()=>n.value.target||(s.value?"_blank":void 0)),a=O(()=>i.value==="_blank"),l=O(()=>!s.value&&!a.value),c=O(()=>n.value.rel||(a.value?"noopener noreferrer":null)),u=O(()=>n.value.ariaLabel??n.value.text),f=O(()=>{if(n.value.exact)return!1;const g=Object.keys(o.value.locales);return g.length?g.every(_=>_!==n.value.link):n.value.link!=="/"}),p=O(()=>l.value?n.value.activeMatch?(n.value.activeMatch instanceof RegExp?n.value.activeMatch:new RegExp(n.value.activeMatch,"u")).test(r.path):f.value?r.path.startsWith(n.value.link):r.path===n.value.link:!1);return()=>{const{before:g,after:_,default:b}=t,w=(b==null?void 0:b(n.value))??[g==null?void 0:g(n.value),n.value.text,_==null?void 0:_(n.value)];return l.value?te(Ct,{class:"auto-link",to:n.value.link,active:p.value,"aria-label":u.value},()=>w):te("a",{class:"auto-link external-link",href:n.value.link,"aria-label":u.value,rel:c.value,target:i.value},w)}}}),Cs=me({name:"ClientOnly",setup(e,t){const n=qe(!1);return st(()=>{n.value=!0}),()=>{var r,o;return n.value?(o=(r=t.slots).default)==null?void 0:o.call(r):null}}}),xr=e=>{Mr.value.forEach(t=>t(e))},Ts=me({name:"Content",props:{path:{type:String,required:!1,default:""}},setup(e){const t=bh(),n=O(()=>{if(!e.path)return t.value;const o=dn(e.path);return Au(async()=>o.loader().then(({comp:s})=>s))}),r=In();return ut(r,()=>{xr("updated")},{deep:!0,flush:"post"}),()=>te(n.value,{onVnodeMounted:()=>{xr("mounted")},onVnodeUpdated:()=>{xr("updated")},onVnodeBeforeUnmount:()=>{xr("beforeUnmount")}})}}),Lh="Layout",Ph="en-US",nn=lr({resolveLayouts:e=>e.reduce((t,n)=>({...t,...n.layouts}),{}),resolvePageHead:(e,t,n)=>{const r=pt(t.description)?t.description:n.description,o=[...Array.isArray(t.head)?t.head:[],...n.head,["title",{},e],["meta",{name:"description",content:r}]];return fd(o)},resolvePageHeadTitle:(e,t)=>[e.title,t.title].filter(n=>!!n).join(" | "),resolvePageLang:(e,t)=>e.lang||t.lang||Ph,resolvePageLayout:(e,t)=>{const n=pt(e.frontmatter.layout)?e.frontmatter.layout:Lh;if(!t[n])throw new Error(`[vuepress] Cannot resolve layout: ${n}`);return t[n]},resolveRouteLocale:(e,t)=>id(e,decodeURI(t)),resolveSiteLocaleData:({base:e,locales:t,...n},r)=>{var o;return{...n,...t[r],head:[...((o=t[r])==null?void 0:o.head)??[],...n.head]}}}),Qt=(e={})=>e,xs=e=>mr(e)?e:`/${kl(e)}`,Rh=Object.defineProperty,$h=(e,t)=>{for(var n in t)Rh(e,n,{get:t[n],enumerable:!0})},Oh={};$h(Oh,{COMPONENT_STATE_TYPE:()=>Ih,INSPECTOR_ID:()=>Hh,INSPECTOR_LABEL:()=>Mh,INSPECTOR_NODES:()=>Dh,INSPECTOR_STATE_SECTION_NAME:()=>Nh,PLUGIN_ID:()=>Vl,PLUGIN_LABEL:()=>As});var Vl="org.vuejs.vuepress",As="VuePress",Ih=As,Hh=Vl,Mh=As,Fi={id:"INTERNAL",label:"Internal",keys:["layouts","routes","redirects"]},Vi={id:"SITE",label:"Site",keys:["siteData","siteLocaleData"]},zi={id:"ROUTE",label:"Route",keys:["routePath","routeLocale"]},Ui={id:"PAGE",label:"Page",keys:["pageData","pageFrontmatter","pageLang","pageHead","pageHeadTitle","pageLayout","pageComponent"]},Dh={[Fi.id]:Fi,[Vi.id]:Vi,[zi.id]:zi,[Ui.id]:Ui},Nh="State";function zl(e){return da()?(Nc(e),!0):!1}const xn=new WeakMap,Ul=(...e)=>{var t;const n=e[0],r=(t=hn())==null?void 0:t.proxy;if(r==null&&!tl())throw new Error("injectLocal must be called in setup");return r&&xn.has(r)&&n in xn.get(r)?xn.get(r)[n]:Fe(...e)};function Bh(e,t){var n;const r=(n=hn())==null?void 0:n.proxy;if(r==null)throw new Error("provideLocal must be called in setup");xn.has(r)||xn.set(r,Object.create(null));const o=xn.get(r);return o[e]=t,Kt(e,t)}const ks=typeof window<"u"&&typeof document<"u";typeof WorkerGlobalScope<"u"&&globalThis instanceof WorkerGlobalScope;const jh=Object.prototype.toString,Fh=e=>jh.call(e)==="[object Object]",Xo=()=>{};function Vh(...e){if(e.length!==1)return Ia(...e);const t=e[0];return typeof t=="function"?$n($a(()=>({get:t,set:Xo}))):qe(t)}function Wl(e,t){function n(...r){return new Promise((o,s)=>{Promise.resolve(e(()=>t.apply(this,r),{fn:t,thisArg:this,args:r})).then(o).catch(s)})}return n}const ql=e=>e();function zh(e,t={}){let n,r,o=Xo;const s=l=>{clearTimeout(l),o(),o=Xo};let i;return l=>{const c=He(e),u=He(t.maxWait);return n&&s(n),c<=0||u!==void 0&&u<=0?(r&&(s(r),r=void 0),Promise.resolve(l())):new Promise((f,p)=>{o=t.rejectOnCancel?p:f,i=l,u&&!r&&(r=setTimeout(()=>{n&&s(n),r=void 0,f(i())},u)),n=setTimeout(()=>{r&&s(r),r=void 0,f(l())},c)})}}function Uh(e=ql,t={}){const{initialState:n="active"}=t,r=Vh(n==="active");function o(){r.value=!1}function s(){r.value=!0}const i=(...a)=>{r.value&&e(...a)};return{isActive:$n(r),pause:o,resume:s,eventFilter:i}}function Wh(e){let t;function n(){return t||(t=e()),t}return n.reset=async()=>{const r=t;t=void 0,r&&await r},n}function Wi(e){return e.endsWith("rem")?Number.parseFloat(e)*16:Number.parseFloat(e)}function Io(e){return Array.isArray(e)?e:[e]}function qh(e){return hn()}function Kh(e,t=200,n={}){return Wl(zh(t,n),e)}function Gh(e,t,n={}){const{eventFilter:r=ql,...o}=n;return ut(e,Wl(r,t),o)}function Zh(e,t,n={}){const{eventFilter:r,initialState:o="active",...s}=n,{eventFilter:i,pause:a,resume:l,isActive:c}=Uh(r,{initialState:o});return{stop:Gh(e,t,{...s,eventFilter:i}),pause:a,resume:l,isActive:c}}function Jh(e,t=!0,n){qh()?st(e,n):t?e():fr(e)}function Yh(e,t,n={}){const{immediate:r=!0,immediateCallback:o=!1}=n,s=Pe(!1);let i;function a(){i&&(clearTimeout(i),i=void 0)}function l(){s.value=!1,a()}function c(...u){o&&e(),a(),s.value=!0,i=setTimeout(()=>{s.value=!1,i=void 0,e(...u)},He(t))}return r&&(s.value=!0,ks&&c()),zl(l),{isPending:ou(s),start:c,stop:l}}function Ls(e=!1,t={}){const{truthyValue:n=!0,falsyValue:r=!1}=t,o=Oe(e),s=Pe(e);function i(a){if(arguments.length)return s.value=a,s.value;{const l=He(n);return s.value=s.value===l?He(r):l,s.value}}return o?i:[s,i]}function Ps(e,t,n){return ut(e,t,{...n,immediate:!0})}const Zr=ks?window:void 0,Kl=ks?window.navigator:void 0;function Qh(e){var t;const n=He(e);return(t=n==null?void 0:n.$el)!=null?t:n}function ft(...e){const t=[],n=()=>{t.forEach(a=>a()),t.length=0},r=(a,l,c,u)=>(a.addEventListener(l,c,u),()=>a.removeEventListener(l,c,u)),o=O(()=>{const a=Io(He(e[0])).filter(l=>l!=null);return a.every(l=>typeof l!="string")?a:void 0}),s=Ps(()=>{var a,l;return[(l=(a=o.value)==null?void 0:a.map(c=>Qh(c)))!=null?l:[Zr].filter(c=>c!=null),Io(He(o.value?e[1]:e[0])),Io(B(o.value?e[2]:e[1])),He(o.value?e[3]:e[2])]},([a,l,c,u])=>{if(n(),!(a!=null&&a.length)||!(l!=null&&l.length)||!(c!=null&&c.length))return;const f=Fh(u)?{...u}:u;t.push(...a.flatMap(p=>l.flatMap(g=>c.map(_=>r(p,g,_,f)))))},{flush:"post"}),i=()=>{s(),n()};return zl(n),i}function Xh(){const e=Pe(!1),t=hn();return t&&st(()=>{e.value=!0},t),e}function Rs(e){const t=Xh();return O(()=>(t.value,!!e()))}const ep=Symbol("vueuse-ssr-width");function tp(){const e=tl()?Ul(ep,null):null;return typeof e=="number"?e:void 0}function Gl(e,t={}){const{window:n=Zr,ssrWidth:r=tp()}=t,o=Rs(()=>n&&"matchMedia"in n&&typeof n.matchMedia=="function"),s=Pe(typeof r=="number"),i=Pe(),a=Pe(!1),l=c=>{a.value=c.matches};return of(()=>{if(s.value){s.value=!o.value;const c=He(e).split(",");a.value=c.some(u=>{const f=u.includes("not all"),p=u.match(/\(\s*min-width:\s*(-?\d+(?:\.\d*)?[a-z]+\s*)\)/),g=u.match(/\(\s*max-width:\s*(-?\d+(?:\.\d*)?[a-z]+\s*)\)/);let _=!!(p||g);return p&&_&&(_=r>=Wi(p[1])),g&&_&&(_=r<=Wi(g[1])),f?!_:_});return}o.value&&(i.value=n.matchMedia(He(e)),a.value=i.value.matches)}),ft(i,"change",l,{passive:!0}),O(()=>a.value)}function qi(e,t={}){const{controls:n=!1,navigator:r=Kl}=t,o=Rs(()=>r&&"permissions"in r),s=Pe(),i=typeof e=="string"?{name:e}:e,a=Pe(),l=()=>{var u,f;a.value=(f=(u=s.value)==null?void 0:u.state)!=null?f:"prompt"};ft(s,"change",l,{passive:!0});const c=Wh(async()=>{if(o.value){if(!s.value)try{s.value=await r.permissions.query(i)}catch{s.value=void 0}finally{l()}if(n)return he(s.value)}});return c(),n?{state:a,isSupported:o,query:c}:a}function np(e={}){const{navigator:t=Kl,read:n=!1,source:r,copiedDuring:o=1500,legacy:s=!1}=e,i=Rs(()=>t&&"clipboard"in t),a=qi("clipboard-read"),l=qi("clipboard-write"),c=O(()=>i.value||s),u=Pe(""),f=Pe(!1),p=Yh(()=>f.value=!1,o,{immediate:!1});async function g(){let k=!(i.value&&A(a.value));if(!k)try{u.value=await t.clipboard.readText()}catch{k=!0}k&&(u.value=w())}c.value&&n&&ft(["copy","cut"],g,{passive:!0});async function _(k=He(r)){if(c.value&&k!=null){let m=!(i.value&&A(l.value));if(!m)try{await t.clipboard.writeText(k)}catch{m=!0}m&&b(k),u.value=k,f.value=!0,p.start()}}function b(k){const m=document.createElement("textarea");m.value=k??"",m.style.position="absolute",m.style.opacity="0",document.body.appendChild(m),m.select(),document.execCommand("copy"),m.remove()}function w(){var k,m,y;return(y=(m=(k=document==null?void 0:document.getSelection)==null?void 0:k.call(document))==null?void 0:m.toString())!=null?y:""}function A(k){return k==="granted"||k==="prompt"}return{isSupported:c,text:u,copied:f,copy:_}}const Ar=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},kr="__vueuse_ssr_handlers__",rp=op();function op(){return kr in Ar||(Ar[kr]=Ar[kr]||{}),Ar[kr]}function sp(e,t){return rp[e]||t}function ip(e){return Gl("(prefers-color-scheme: dark)",e)}function ap(e){return e==null?"any":e instanceof Set?"set":e instanceof Map?"map":e instanceof Date?"date":typeof e=="boolean"?"boolean":typeof e=="string"?"string":typeof e=="object"?"object":Number.isNaN(e)?"any":"number"}const lp={boolean:{read:e=>e==="true",write:e=>String(e)},object:{read:e=>JSON.parse(e),write:e=>JSON.stringify(e)},number:{read:e=>Number.parseFloat(e),write:e=>String(e)},any:{read:e=>e,write:e=>String(e)},string:{read:e=>e,write:e=>String(e)},map:{read:e=>new Map(JSON.parse(e)),write:e=>JSON.stringify(Array.from(e.entries()))},set:{read:e=>new Set(JSON.parse(e)),write:e=>JSON.stringify(Array.from(e))},date:{read:e=>new Date(e),write:e=>e.toISOString()}},Ki="vueuse-storage";function $s(e,t,n,r={}){var o;const{flush:s="pre",deep:i=!0,listenToStorageChanges:a=!0,writeDefaults:l=!0,mergeDefaults:c=!1,shallow:u,window:f=Zr,eventFilter:p,onError:g=D=>{console.error(D)},initOnMounted:_}=r,b=(u?Pe:qe)(typeof t=="function"?t():t),w=O(()=>He(e));if(!n)try{n=sp("getDefaultStorage",()=>{var D;return(D=Zr)==null?void 0:D.localStorage})()}catch(D){g(D)}if(!n)return b;const A=He(t),k=ap(A),m=(o=r.serializer)!=null?o:lp[k],{pause:y,resume:N}=Zh(b,()=>I(b.value),{flush:s,deep:i,eventFilter:p});ut(w,()=>E(),{flush:s});let Y=!1;const M=D=>{_&&!Y||E(D)},S=D=>{_&&!Y||H(D)};f&&a&&(n instanceof Storage?ft(f,"storage",M,{passive:!0}):ft(f,Ki,S)),_?Jh(()=>{Y=!0,E()}):E();function W(D,X){if(f){const L={key:w.value,oldValue:D,newValue:X,storageArea:n};f.dispatchEvent(n instanceof Storage?new StorageEvent("storage",L):new CustomEvent(Ki,{detail:L}))}}function I(D){try{const X=n.getItem(w.value);if(D==null)W(X,null),n.removeItem(w.value);else{const L=m.write(D);X!==L&&(n.setItem(w.value,L),W(X,L))}}catch(X){g(X)}}function Q(D){const X=D?D.newValue:n.getItem(w.value);if(X==null)return l&&A!=null&&n.setItem(w.value,m.write(A)),A;if(!D&&c){const L=m.read(X);return typeof c=="function"?c(L,A):k==="object"&&!Array.isArray(L)?{...A,...L}:L}else return typeof X!="string"?X:m.read(X)}function E(D){if(!(D&&D.storageArea!==n)){if(D&&D.key==null){b.value=A;return}if(!(D&&D.key!==w.value)){y();try{(D==null?void 0:D.newValue)!==m.write(b.value)&&(b.value=Q(D))}catch(X){g(X)}finally{D?fr(N):N()}}}}function H(D){E(D.detail)}return b}const Gi=async(e,t)=>{const{path:n,query:r}=e.currentRoute.value,{scrollBehavior:o}=e.options;e.options.scrollBehavior=void 0,await e.replace({path:n,query:r,hash:t}),e.options.scrollBehavior=o},cp=({headerLinkSelector:e,headerAnchorSelector:t,delay:n,offset:r=5})=>{const o=vr();ft("scroll",Kh(()=>{var _,b;const i=Math.max(window.scrollY,document.documentElement.scrollTop,document.body.scrollTop);if(Math.abs(i-0)<r){Gi(o,"");return}const l=window.innerHeight+i,c=Math.max(document.documentElement.scrollHeight,document.body.scrollHeight),u=Math.abs(c-l)<r,f=Array.from(document.querySelectorAll(e)),g=Array.from(document.querySelectorAll(t)).filter(w=>f.some(A=>A.hash===w.hash));for(let w=0;w<g.length;w++){const A=g[w],k=g[w+1],m=i>=(((_=A.parentElement)==null?void 0:_.offsetTop)??0)-r,y=!k||i<(((b=k.parentElement)==null?void 0:b.offsetTop)??0)-r;if(!(m&&y))continue;const Y=decodeURIComponent(o.currentRoute.value.hash),M=decodeURIComponent(A.hash);if(Y===M)return;if(u){for(let S=w+1;S<g.length;S++)if(Y===decodeURIComponent(g[S].hash))return}Gi(o,M);return}},n))},up="a.vp-sidebar-item",fp=".header-anchor",dp=300,hp=5,pp=Qt({setup(){cp({headerLinkSelector:up,headerAnchorSelector:fp,delay:dp,offset:hp})}}),gp=Object.freeze(Object.defineProperty({__proto__:null,default:pp},Symbol.toStringTag,{value:"Module"})),Zl=[...new Array(6)].map((e,t)=>`[vp-content] h${t+1}`).join(","),mp=(e,t=2)=>{if(t===!1)return[];const[n,r]=typeof t=="number"?[t,t]:t==="deep"?[2,6]:t,o=e.filter(i=>i.level>=n&&i.level<=r),s=[];e:for(let i=0;i<o.length;i++){const a=o[i];if(i===0)s.push(a);else{for(let l=i-1;l>=0;l--){const c=o[l];if(c.level<a.level){c.children.push(a);continue e}}s.push(a)}}return s},vp=(e,t=[])=>{let n;if(t.length){const r=e.cloneNode(!0);r.querySelectorAll(t.join(",")).forEach(o=>{o.remove()}),n=r.textContent||""}else n=e.textContent||"";return n.trim()},_p=(e=Zl,t=[])=>Array.from(document.querySelectorAll(e)).filter(n=>n.id&&n.hasChildNodes()).map(n=>({element:n,title:vp(n,t),link:`#${n.id}`,slug:n.id,level:Number(n.tagName[1]),children:[]})),yp=({selector:e=Zl,levels:t=2,ignore:n=[]}={})=>mp(_p(e,n),t),Jl=(e,t)=>{var r;const n=(r=hn())==null?void 0:r.appContext.components;return n?e in n||Qe(e)in n||ar(Qe(e))in n:!1},Yl=e=>{const t=Ss();return O(()=>He(e)[t.value]??{})},bp=()=>{const e=Sh();return O(()=>Object.keys(e.value))},Ep=e=>typeof e<"u",Ql=(e,t)=>pt(e)&&e.startsWith(t),{keys:wp}=Object,Xl=e=>Ql(e,"/")&&e[1]!=="/",ec=e=>!rd(e)&&!gr(e),Sp=/language-(shellscript|shell|bash|sh|zsh)/,Cp=({duration:e=2e3,locales:t,selector:n,showInMobile:r,ignoreSelector:o=[],transform:s})=>{const i=Gl("(max-width: 419px)"),a=O(()=>!i.value||r),l=Yl(t),c=_=>{var w;if(_.hasAttribute("copy-code"))return;const b=document.createElement("button");b.type="button",b.classList.add("vp-copy-code-button"),b.setAttribute("aria-label",l.value.copy),b.setAttribute("data-copied",l.value.copied),(w=_.parentElement)==null||w.insertBefore(b,_),_.setAttribute("copy-code","")},u=()=>{document.body.classList.toggle("no-copy-code",!a.value),a.value&&document.querySelectorAll(n.join(",")).forEach(c)};Ps(a,u,{flush:"post"}),_r(_=>{_!=="beforeUnmount"&&u()});const{copy:f}=np({legacy:!0}),p=new WeakMap,g=async(_,b,w)=>{const A=b.cloneNode(!0);o.length&&A.querySelectorAll(o.join(",")).forEach(y=>{y.remove()}),s&&s(A);let k=A.textContent||"";if(Sp.test(_.className)&&(k=k.replace(/^ *(\$|>) /gm,"")),await f(k),e<=0)return;w.classList.add("copied"),clearTimeout(p.get(w));const m=setTimeout(()=>{w.classList.remove("copied"),w.blur(),p.delete(w)},e);p.set(w,m)};ft("click",_=>{const b=_.target;if(a.value&&b.matches('div[class*="language-"] > button.vp-copy-code-button')){const w=b.parentElement,A=b.nextElementSibling;if(!w||!A)return;g(w,A,b)}})};var Tp=[],xp={"/":{copy:"复制代码",copied:"已复制"}},Ap=['[vp-content] div[class*="language-"] pre'];const kp=Qt({setup:()=>{Cp({selector:Ap,ignoreSelector:Tp,locales:xp,duration:2e3,showInMobile:!1})}}),Lp=Object.freeze(Object.defineProperty({__proto__:null,default:kp},Symbol.toStringTag,{value:"Module"})),Pp=Qt({setup(){ft("beforeprint",()=>{document.querySelectorAll("details").forEach(e=>{e.open=!0})})}}),Rp=Object.freeze(Object.defineProperty({__proto__:null,default:Pp},Symbol.toStringTag,{value:"Module"}));var Zi={provider:null,pattern:{},repo:""};const Ji=typeof Zi>"u"?{}:Zi,$p=(e,t)=>!e||mr(e)?e:t==="github"?`https://github.com/${e}`:t==="gitee"?`https://gitee.com/${e}`:e,Op=/#(\d+)/g,Ip=(e=!0)=>{const t=In(),n=ws(),r=co(),{pattern:o={},provider:s}=Ji,i=$p(Ji.repo,s);return O(()=>{var l;if(t.value.changelog===!1||!He(e))return[];const a=new Intl.DateTimeFormat(n.value,{dateStyle:"short"});return(((l=r.value.git)==null?void 0:l.changelog)??[]).map(c=>{const u={date:a.format(c.time),...c};return o.issue&&i&&(u.message=u.message.replace(Op,(f,p)=>`<a href="${o.issue.replace(":issue",p).replace(":repo",i)}" target="_blank" rel="noopener noreferrer">${f}</a>`)),o.commit&&i&&(u.commitUrl=o.commit.replace(":hash",u.hash).replace(":repo",i)),o.tag&&i&&u.tag&&(u.tagUrl=o.tag.replace(":tag",u.tag).replace(":repo",i)),u})})},tc=(e=!0)=>{const t=In(),n=co();return O(()=>{var r;return t.value.contributors===!1||!He(e)?[]:((r=n.value.git)==null?void 0:r.contributors)??[]})};var Yi={"/":{contributors:"贡献者",changelog:"更新日志",timeOn:"于",viewChangelog:"查看所有更新日志",latestUpdateAt:"最近更新："}};const Hp=typeof Yi>"u"?{}:Yi,Os=()=>Yl(Hp),nc=(e=!0)=>{const t=ws(),n=Os(),r=co();return O(()=>{var a,l,c;if(!He(e))return null;const o=((a=r.value.git)==null?void 0:a.updatedTime)??((c=(l=r.value.git)==null?void 0:l.changelog)==null?void 0:c[0].time);if(!o)return null;const s=new Date(o),i=new Intl.DateTimeFormat(t.value,{dateStyle:"short",timeStyle:"short"}).format(o);return{date:s,text:i,iso:s.toISOString(),locale:n.value.latestUpdateAt}})},rc=({level:e=2,text:t,anchor:n})=>te(`h${e||2}`,{id:n,tabindex:"-1"},te("a",{href:`#${n}`,class:"header-anchor"},te("span",t))),Mp=({name:e,url:t,avatar:n})=>te(t?"a":"span",{href:t,target:"_blank",rel:"noreferrer",class:"vp-contributor"},[n?te("img",{src:n,alt:"",class:"vp-contributor-avatar"}):null,te("span",{class:"vp-contributor-name"},e)]),Dp=me({name:"GitContributors",props:{title:String,headerLevel:{type:Number,default:2}},setup(e){const t=tc(),n=Os();return()=>t.value.length?[te(rc,{level:e.headerLevel,anchor:"doc-contributors",text:e.title||n.value.contributors}),te("div",{class:"vp-contributors"},t.value.map(r=>te(Mp,r)))]:null}}),Np=me({name:"GitChangelog",props:{title:String,headerLevel:{type:Number,default:2}},setup(e){const t=Ip(),n=Os(),r=nc(),[o,s]=Ls(),i=()=>te("div",{class:"vp-changelog-header",onClick:()=>s()},[te("div",{class:"vp-latest-updated"},[te("span",{class:"vp-changelog-icon"}),te("span",{"data-allow-mismatch":""},r.value.text)]),te("div",[te("span",{class:"vp-changelog-menu-icon"}),te("span",n.value.viewChangelog)])]),a=({item:c})=>te("li",{class:"vp-changelog-item-tag"},te("div",[te("a",{class:"vp-changelog-tag"},te("code",c.tag)),te("span",{class:"vp-changelog-date","data-allow-mismatch":""},[n.value.timeOn," ",te("time",{datetime:new Date(c.time).toISOString()},c.date)])])),l=({item:c})=>te("li",{class:"vp-changelog-item-commit"},[te(c.commitUrl?"a":"span",{class:"vp-changelog-hash",href:c.commitUrl,target:"_blank",rel:"noreferrer"},[te("code",c.hash.slice(0,5))]),te("span",{class:"vp-changelog-divider"},"-"),te("span",{class:"vp-changelog-message",innerHTML:c.message}),te("span",{class:"vp-changelog-date","data-allow-mismatch":""},[n.value.timeOn||"on"," ",te("time",{datetime:new Date(c.time).toISOString()},c.date)])]);return()=>t.value.length?[te(rc,{level:e.headerLevel,anchor:"doc-changelog",text:e.title||n.value.changelog}),te("div",{class:["vp-changelog-wrapper",{active:o.value}]},[te(i),te("ul",{class:"vp-changelog-list"},[t.value.map(c=>c.tag?te(a,{item:c,key:c.tag}):te(l,{item:c,key:c.hash}))])])]:null}}),Bp={enhance:({app:e})=>{e.component("GitContributors",Dp),e.component("GitChangelog",Np)}},jp=Object.freeze(Object.defineProperty({__proto__:null,default:Bp},Symbol.toStringTag,{value:"Module"}));/*! medium-zoom 1.1.0 | MIT License | https://github.com/francoischalifour/medium-zoom */var on=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Lr=function(t){return t.tagName==="IMG"},Fp=function(t){return NodeList.prototype.isPrototypeOf(t)},Dr=function(t){return t&&t.nodeType===1},Qi=function(t){var n=t.currentSrc||t.src;return n.substr(-4).toLowerCase()===".svg"},Xi=function(t){try{return Array.isArray(t)?t.filter(Lr):Fp(t)?[].slice.call(t).filter(Lr):Dr(t)?[t].filter(Lr):typeof t=="string"?[].slice.call(document.querySelectorAll(t)).filter(Lr):[]}catch{throw new TypeError(`The provided selector is invalid.
Expects a CSS selector, a Node element, a NodeList or an array.
See: https://github.com/francoischalifour/medium-zoom`)}},Vp=function(t){var n=document.createElement("div");return n.classList.add("medium-zoom-overlay"),n.style.background=t,n},zp=function(t){var n=t.getBoundingClientRect(),r=n.top,o=n.left,s=n.width,i=n.height,a=t.cloneNode(),l=window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0,c=window.pageXOffset||document.documentElement.scrollLeft||document.body.scrollLeft||0;return a.removeAttribute("id"),a.style.position="absolute",a.style.top=r+l+"px",a.style.left=o+c+"px",a.style.width=s+"px",a.style.height=i+"px",a.style.transform="",a},mn=function(t,n){var r=on({bubbles:!1,cancelable:!1,detail:void 0},n);if(typeof window.CustomEvent=="function")return new CustomEvent(t,r);var o=document.createEvent("CustomEvent");return o.initCustomEvent(t,r.bubbles,r.cancelable,r.detail),o},Up=function e(t){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=window.Promise||function(E){function H(){}E(H,H)},o=function(E){var H=E.target;if(H===W){_();return}m.indexOf(H)!==-1&&b({target:H})},s=function(){if(!(N||!S.original)){var E=window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0;Math.abs(Y-E)>M.scrollOffset&&setTimeout(_,150)}},i=function(E){var H=E.key||E.keyCode;(H==="Escape"||H==="Esc"||H===27)&&_()},a=function(){var E=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},H=E;if(E.background&&(W.style.background=E.background),E.container&&E.container instanceof Object&&(H.container=on({},M.container,E.container)),E.template){var D=Dr(E.template)?E.template:document.querySelector(E.template);H.template=D}return M=on({},M,H),m.forEach(function(X){X.dispatchEvent(mn("medium-zoom:update",{detail:{zoom:I}}))}),I},l=function(){var E=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};return e(on({},M,E))},c=function(){for(var E=arguments.length,H=Array(E),D=0;D<E;D++)H[D]=arguments[D];var X=H.reduce(function(L,re){return[].concat(L,Xi(re))},[]);return X.filter(function(L){return m.indexOf(L)===-1}).forEach(function(L){m.push(L),L.classList.add("medium-zoom-image")}),y.forEach(function(L){var re=L.type,K=L.listener,be=L.options;X.forEach(function(De){De.addEventListener(re,K,be)})}),I},u=function(){for(var E=arguments.length,H=Array(E),D=0;D<E;D++)H[D]=arguments[D];S.zoomed&&_();var X=H.length>0?H.reduce(function(L,re){return[].concat(L,Xi(re))},[]):m;return X.forEach(function(L){L.classList.remove("medium-zoom-image"),L.dispatchEvent(mn("medium-zoom:detach",{detail:{zoom:I}}))}),m=m.filter(function(L){return X.indexOf(L)===-1}),I},f=function(E,H){var D=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return m.forEach(function(X){X.addEventListener("medium-zoom:"+E,H,D)}),y.push({type:"medium-zoom:"+E,listener:H,options:D}),I},p=function(E,H){var D=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return m.forEach(function(X){X.removeEventListener("medium-zoom:"+E,H,D)}),y=y.filter(function(X){return!(X.type==="medium-zoom:"+E&&X.listener.toString()===H.toString())}),I},g=function(){var E=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},H=E.target,D=function(){var L={width:document.documentElement.clientWidth,height:document.documentElement.clientHeight,left:0,top:0,right:0,bottom:0},re=void 0,K=void 0;if(M.container)if(M.container instanceof Object)L=on({},L,M.container),re=L.width-L.left-L.right-M.margin*2,K=L.height-L.top-L.bottom-M.margin*2;else{var be=Dr(M.container)?M.container:document.querySelector(M.container),De=be.getBoundingClientRect(),Ze=De.width,Ve=De.height,Bt=De.left,jt=De.top;L=on({},L,{width:Ze,height:Ve,left:Bt,top:jt})}re=re||L.width-M.margin*2,K=K||L.height-M.margin*2;var _t=S.zoomedHd||S.original,Je=Qi(_t)?re:_t.naturalWidth||re,x=Qi(_t)?K:_t.naturalHeight||K,Z=_t.getBoundingClientRect(),U=Z.top,ee=Z.left,fe=Z.width,we=Z.height,d=Math.min(Math.max(fe,Je),re)/fe,h=Math.min(Math.max(we,x),K)/we,v=Math.min(d,h),P=(-ee+(re-fe)/2+M.margin+L.left)/v,C=(-U+(K-we)/2+M.margin+L.top)/v,R="scale("+v+") translate3d("+P+"px, "+C+"px, 0)";S.zoomed.style.transform=R,S.zoomedHd&&(S.zoomedHd.style.transform=R)};return new r(function(X){if(H&&m.indexOf(H)===-1){X(I);return}var L=function Ze(){N=!1,S.zoomed.removeEventListener("transitionend",Ze),S.original.dispatchEvent(mn("medium-zoom:opened",{detail:{zoom:I}})),X(I)};if(S.zoomed){X(I);return}if(H)S.original=H;else if(m.length>0){var re=m;S.original=re[0]}else{X(I);return}if(S.original.dispatchEvent(mn("medium-zoom:open",{detail:{zoom:I}})),Y=window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0,N=!0,S.zoomed=zp(S.original),document.body.appendChild(W),M.template){var K=Dr(M.template)?M.template:document.querySelector(M.template);S.template=document.createElement("div"),S.template.appendChild(K.content.cloneNode(!0)),document.body.appendChild(S.template)}if(S.original.parentElement&&S.original.parentElement.tagName==="PICTURE"&&S.original.currentSrc&&(S.zoomed.src=S.original.currentSrc),document.body.appendChild(S.zoomed),window.requestAnimationFrame(function(){document.body.classList.add("medium-zoom--opened")}),S.original.classList.add("medium-zoom-image--hidden"),S.zoomed.classList.add("medium-zoom-image--opened"),S.zoomed.addEventListener("click",_),S.zoomed.addEventListener("transitionend",L),S.original.getAttribute("data-zoom-src")){S.zoomedHd=S.zoomed.cloneNode(),S.zoomedHd.removeAttribute("srcset"),S.zoomedHd.removeAttribute("sizes"),S.zoomedHd.removeAttribute("loading"),S.zoomedHd.src=S.zoomed.getAttribute("data-zoom-src"),S.zoomedHd.onerror=function(){clearInterval(be),console.warn("Unable to reach the zoom image target "+S.zoomedHd.src),S.zoomedHd=null,D()};var be=setInterval(function(){S.zoomedHd.complete&&(clearInterval(be),S.zoomedHd.classList.add("medium-zoom-image--opened"),S.zoomedHd.addEventListener("click",_),document.body.appendChild(S.zoomedHd),D())},10)}else if(S.original.hasAttribute("srcset")){S.zoomedHd=S.zoomed.cloneNode(),S.zoomedHd.removeAttribute("sizes"),S.zoomedHd.removeAttribute("loading");var De=S.zoomedHd.addEventListener("load",function(){S.zoomedHd.removeEventListener("load",De),S.zoomedHd.classList.add("medium-zoom-image--opened"),S.zoomedHd.addEventListener("click",_),document.body.appendChild(S.zoomedHd),D()})}else D()})},_=function(){return new r(function(E){if(N||!S.original){E(I);return}var H=function D(){S.original.classList.remove("medium-zoom-image--hidden"),document.body.removeChild(S.zoomed),S.zoomedHd&&document.body.removeChild(S.zoomedHd),document.body.removeChild(W),S.zoomed.classList.remove("medium-zoom-image--opened"),S.template&&document.body.removeChild(S.template),N=!1,S.zoomed.removeEventListener("transitionend",D),S.original.dispatchEvent(mn("medium-zoom:closed",{detail:{zoom:I}})),S.original=null,S.zoomed=null,S.zoomedHd=null,S.template=null,E(I)};N=!0,document.body.classList.remove("medium-zoom--opened"),S.zoomed.style.transform="",S.zoomedHd&&(S.zoomedHd.style.transform=""),S.template&&(S.template.style.transition="opacity 150ms",S.template.style.opacity=0),S.original.dispatchEvent(mn("medium-zoom:close",{detail:{zoom:I}})),S.zoomed.addEventListener("transitionend",H)})},b=function(){var E=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},H=E.target;return S.original?_():g({target:H})},w=function(){return M},A=function(){return m},k=function(){return S.original},m=[],y=[],N=!1,Y=0,M=n,S={original:null,zoomed:null,zoomedHd:null,template:null};Object.prototype.toString.call(t)==="[object Object]"?M=t:(t||typeof t=="string")&&c(t),M=on({margin:0,background:"#fff",scrollOffset:40,container:null,template:null},M);var W=Vp(M.background);document.addEventListener("click",o),document.addEventListener("keyup",i),document.addEventListener("scroll",s),window.addEventListener("resize",_);var I={open:g,close:_,toggle:b,update:a,clone:l,attach:c,detach:u,on:f,off:p,getOptions:w,getImages:A,getZoomedImage:k};return I};function Wp(e,t){t===void 0&&(t={});var n=t.insertAt;if(!(typeof document>"u")){var r=document.head||document.getElementsByTagName("head")[0],o=document.createElement("style");o.type="text/css",n==="top"&&r.firstChild?r.insertBefore(o,r.firstChild):r.appendChild(o),o.styleSheet?o.styleSheet.cssText=e:o.appendChild(document.createTextNode(e))}}var qp=".medium-zoom-overlay{position:fixed;top:0;right:0;bottom:0;left:0;opacity:0;transition:opacity .3s;will-change:opacity}.medium-zoom--opened .medium-zoom-overlay{cursor:pointer;cursor:zoom-out;opacity:1}.medium-zoom-image{cursor:pointer;cursor:zoom-in;transition:transform .3s cubic-bezier(.2,0,.2,1)!important}.medium-zoom-image--hidden{visibility:hidden}.medium-zoom-image--opened{position:relative;cursor:pointer;cursor:zoom-out;will-change:transform}";Wp(qp);const oc=Symbol("mediumZoom"),Kp=()=>{const e=Fe(oc);if(!e)throw new Error("useMediumZoom() is called without provider.");return e};var Gp={};const Zp="[vp-content] > img, [vp-content] :not(a) > img",Jp=Gp,Yp=Qt({enhance({app:e}){const t=Up(Jp);t.refresh=(n=Zp)=>{t.detach(),t.attach(n)},e.provide(oc,t)},setup(){const e=Kp();_r(t=>{t!=="beforeUnmount"&&e.refresh()})}}),Qp=Object.freeze(Object.defineProperty({__proto__:null,default:Yp},Symbol.toStringTag,{value:"Module"}));/**
 * NProgress, (c) 2013, 2014 Rico Sta. Cruz - http://ricostacruz.com/nprogress
 * @license MIT
 */const ea=(e,t)=>{e.classList.add(t)},ta=(e,t)=>{e.classList.remove(t)},Xp=e=>{var t;(t=e==null?void 0:e.parentNode)==null||t.removeChild(e)},Ho=(e,t,n)=>e<t?t:e>n?n:e,na=e=>(-1+e)*100,eg=(()=>{const e=[],t=()=>{const n=e.shift();n&&n(t)};return n=>{e.push(n),e.length===1&&t()}})(),tg=e=>e.replace(/^-ms-/,"ms-").replace(/-([\da-z])/gi,(t,n)=>n.toUpperCase()),Pr=(()=>{const e=["Webkit","O","Moz","ms"],t={},n=s=>{const{style:i}=document.body;if(s in i)return s;const a=s.charAt(0).toUpperCase()+s.slice(1);let l=e.length;for(;l--;){const c=`${e[l]}${a}`;if(c in i)return c}return s},r=s=>{const i=tg(s);return t[i]??(t[i]=n(i))},o=(s,i,a)=>{s.style[r(i)]=a};return(s,i)=>{for(const a in i){const l=i[a];Object.hasOwn(i,a)&&Ep(l)&&o(s,a,l)}}})(),Lt={minimum:.08,easing:"ease",speed:200,trickleRate:.02,trickleSpeed:800,barSelector:'[role="bar"]',parent:"body",template:'<div class="bar" role="bar"></div>'},Le={percent:null,isRendered:()=>!!document.getElementById("nprogress"),set:e=>{const{speed:t,easing:n}=Lt,r=Le.isStarted(),o=Ho(e,Lt.minimum,1);Le.percent=o===1?null:o;const s=Le.render(!r),i=s.querySelector(Lt.barSelector);return s.offsetWidth,eg(a=>{Pr(i,{transform:`translate3d(${na(o)}%,0,0)`,transition:`all ${t}ms ${n}`}),o===1?(Pr(s,{transition:"none",opacity:"1"}),s.offsetWidth,setTimeout(()=>{Pr(s,{transition:`all ${t}ms linear`,opacity:"0"}),setTimeout(()=>{Le.remove(),a()},t)},t)):setTimeout(()=>{a()},t)}),Le},isStarted:()=>typeof Le.percent=="number",start:()=>{Le.percent||Le.set(0);const e=()=>{setTimeout(()=>{Le.percent&&(Le.trickle(),e())},Lt.trickleSpeed)};return e(),Le},done:e=>!e&&!Le.percent?Le:Le.increase(.3+.5*Math.random()).set(1),increase:e=>{let{percent:t}=Le;return t?(t=Ho(t+(typeof e=="number"?e:(1-t)*Ho(Math.random()*t,.1,.95)),0,.994),Le.set(t)):Le.start()},trickle:()=>Le.increase(Math.random()*Lt.trickleRate),render:e=>{if(Le.isRendered())return document.getElementById("nprogress");ea(document.documentElement,"nprogress-busy");const t=document.createElement("div");t.id="nprogress",t.innerHTML=Lt.template;const n=t.querySelector(Lt.barSelector),r=document.querySelector(Lt.parent),o=e?"-100":na(Le.percent??0);return Pr(n,{transition:"all 0 linear",transform:`translate3d(${o}%,0,0)`}),r&&(r!==document.body&&ea(r,"nprogress-custom-parent"),r.appendChild(t)),t},remove:()=>{ta(document.documentElement,"nprogress-busy"),ta(document.querySelector(Lt.parent),"nprogress-custom-parent"),Xp(document.getElementById("nprogress"))}},ng=()=>{st(()=>{const e=vr(),t=new Set;t.add(e.currentRoute.value.path),e.beforeEach(n=>{t.has(n.path)||Le.start()}),e.afterEach(n=>{t.add(n.path),Le.done()})})},rg=Qt({setup(){ng()}}),og=Object.freeze(Object.defineProperty({__proto__:null,default:rg},Symbol.toStringTag,{value:"Module"})),sg=({selector:e='div[class*="language-"].has-collapsed-lines > .collapsed-lines'}={})=>{ft("click",t=>{const n=t.target;if(n.matches(e)){const r=n.parentElement;r!=null&&r.classList.toggle("collapsed")&&r.scrollIntoView({block:"center",behavior:"instant"})}})},ig={setup(){sg()}},ag=Object.freeze(Object.defineProperty({__proto__:null,default:ig},Symbol.toStringTag,{value:"Module"})),lg="VUEPRESS_CODE_TAB_STORE",Rr=$s(lg,{}),cg=me({name:"CodeTabs",props:{active:{type:Number,default:0},data:{type:Array,required:!0},id:{type:String,required:!0},tabId:String},slots:Object,setup(e,{slots:t}){const n=qe(e.active),r=Pe([]),o=()=>{e.tabId&&(Rr.value[e.tabId]=e.data[n.value].id)},s=(c=n.value)=>{n.value=c<r.value.length-1?c+1:0,r.value[n.value].focus()},i=(c=n.value)=>{n.value=c>0?c-1:r.value.length-1,r.value[n.value].focus()},a=(c,u)=>{c.key===" "||c.key==="Enter"?(c.preventDefault(),n.value=u):c.key==="ArrowRight"?(c.preventDefault(),s()):c.key==="ArrowLeft"&&(c.preventDefault(),i()),e.tabId&&(Rr.value[e.tabId]=e.data[n.value].id)},l=()=>{if(e.tabId){const c=e.data.findIndex(({id:u})=>Rr.value[e.tabId]===u);if(c!==-1)return c}return e.active};return st(()=>{n.value=l(),ut(()=>e.tabId&&Rr.value[e.tabId],(c,u)=>{if(e.tabId&&c!==u){const f=e.data.findIndex(({id:p})=>p===c);f!==-1&&(n.value=f)}})}),()=>e.data.length?te("div",{class:"vp-code-tabs"},[te("div",{class:"vp-code-tabs-nav",role:"tablist"},e.data.map(({id:c},u)=>{const f=u===n.value;return te("button",{type:"button",ref:p=>{p&&(r.value[u]=p)},class:["vp-code-tab-nav",{active:f}],role:"tab","aria-controls":`codetab-${e.id}-${u}`,"aria-selected":f,onClick:()=>{n.value=u,o()},onKeydown:p=>{a(p,u)}},t[`title${u}`]({value:c,isActive:f}))})),e.data.map(({id:c},u)=>{const f=u===n.value;return te("div",{class:["vp-code-tab",{active:f}],id:`codetab-${e.id}-${u}`,role:"tabpanel","aria-expanded":f},[te("div",{class:"vp-code-tab-title"},t[`title${u}`]({value:c,isActive:f})),t[`tab${u}`]({value:c,isActive:f})])})]):null}}),ug="VUEPRESS_TAB_STORE",Mo=$s(ug,{}),fg=me({name:"Tabs",props:{active:{type:Number,default:0},data:{type:Array,required:!0},id:{type:String,required:!0},tabId:String},slots:Object,setup(e,{slots:t}){const n=qe(e.active),r=Pe([]),o=()=>{e.tabId&&(Mo.value[e.tabId]=e.data[n.value].id)},s=(c=n.value)=>{n.value=c<r.value.length-1?c+1:0,r.value[n.value].focus()},i=(c=n.value)=>{n.value=c>0?c-1:r.value.length-1,r.value[n.value].focus()},a=(c,u)=>{c.key===" "||c.key==="Enter"?(c.preventDefault(),n.value=u):c.key==="ArrowRight"?(c.preventDefault(),s()):c.key==="ArrowLeft"&&(c.preventDefault(),i()),o()},l=()=>{if(e.tabId){const c=e.data.findIndex(({id:u})=>Mo.value[e.tabId]===u);if(c!==-1)return c}return e.active};return st(()=>{n.value=l(),ut(()=>e.tabId&&Mo.value[e.tabId],(c,u)=>{if(e.tabId&&c!==u){const f=e.data.findIndex(({id:p})=>p===c);f!==-1&&(n.value=f)}})}),()=>e.data.length?te("div",{class:"vp-tabs"},[te("div",{class:"vp-tabs-nav",role:"tablist"},e.data.map(({id:c},u)=>{const f=u===n.value;return te("button",{type:"button",ref:p=>{p&&(r.value[u]=p)},class:["vp-tab-nav",{active:f}],role:"tab","aria-controls":`tab-${e.id}-${u}`,"aria-selected":f,onClick:()=>{n.value=u,o()},onKeydown:p=>{a(p,u)}},t[`title${u}`]({value:c,isActive:f}))})),e.data.map(({id:c},u)=>{const f=u===n.value;return te("div",{class:["vp-tab",{active:f}],id:`tab-${e.id}-${u}`,role:"tabpanel","aria-expanded":f},[te("div",{class:"vp-tab-title"},t[`title${u}`]({value:c,isActive:f})),t[`tab${u}`]({value:c,isActive:f})])})]):null}}),dg={enhance:({app:e})=>{e.component("CodeTabs",cg),e.component("Tabs",fg)}},hg=Object.freeze(Object.defineProperty({__proto__:null,default:dg},Symbol.toStringTag,{value:"Module"})),pg=JSON.parse(`{"logo":"https://img-public.hui1688.cn/shuangma/logo.jpg","logoDark":"https://img-public.hui1688.cn/shuangma/logo.jpg","navbar":["/",{"text":"公司简介","link":"/company/"},{"text":"产品展示","link":"/products/"},{"text":"生产车间","link":"/workshop/"},{"text":"资质证书","link":"/qualification/"},{"text":"文章","link":"/article/"}],"sidebar":false,"sidebarDepth":0,"editLink":false,"lastUpdated":false,"contributors":false,"locales":{"/":{"selectLanguageName":"English"}},"colorMode":"auto","colorModeSwitch":true,"repo":null,"selectLanguageText":"Languages","selectLanguageAriaLabel":"Select language","editLinkText":"Edit this page","contributorsText":"Contributors","notFound":["There's nothing here.","How did we get here?","That's a Four-Oh-Four.","Looks like we've got some broken links."],"backToHome":"Take me home","openInNewWindow":"open in new window","toggleColorMode":"toggle color mode","toggleSidebar":"toggle sidebar"}`),gg=qe(pg),sc=()=>gg,ic=Symbol(""),mg=()=>{const e=Fe(ic);if(!e)throw new Error("useThemeLocaleData() is called without provider.");return e},vg=(e,t)=>{const{locales:n,...r}=e;return{...r,...n==null?void 0:n[t]}},_g=Qt({enhance({app:e}){const t=sc(),n=e._context.provides[Es],r=O(()=>vg(t.value,n.routeLocale.value));e.provide(ic,r),Object.defineProperties(e.config.globalProperties,{$theme:{get(){return t.value}},$themeLocale:{get(){return r.value}}})}}),yg=Object.freeze(Object.defineProperty({__proto__:null,default:_g},Symbol.toStringTag,{value:"Module"})),Ie=()=>{const{pageData:e,pageFrontmatter:t,pageLang:n,siteData:r,siteLocaleData:o,...s}=vt();return{...s,page:e,frontmatter:t,lang:n,site:r,siteLocale:o,theme:sc(),themeLocale:mg()}},ac=Symbol(""),bg=e=>{const t=(n=e.value)=>{const r=window.document.documentElement;r.dataset.theme=n?"dark":"light"};st(()=>{Ps(e,t)}),hr(()=>{t()})},Is=()=>{const e=Fe(ac);if(!e)throw new Error("useDarkMode() is called without provider.");return e},Eg=()=>{const{themeLocale:e}=Ie(),t=ip(),n=$s("vuepress-color-scheme",e.value.colorMode),r=O({get(){return e.value.colorModeSwitch?n.value==="auto"?t.value:n.value==="dark":e.value.colorMode==="dark"},set(o){o===t.value?n.value="auto":n.value=o?"dark":"light"}});Kt(ac,r),bg(r)},lc=Symbol("headers"),wg=()=>{const e=Ul(lc);if(!e)throw new Error("useHeaders() is called without provider.");return e},Sg=()=>{const{frontmatter:e,themeLocale:t}=Ie(),n=qe([]),r=O(()=>e.value.sidebarDepth??t.value.sidebarDepth??2),o=()=>{if(r.value<=0){n.value=[];return}n.value=yp({levels:[2,r.value+1],ignore:[".vp-badge"]})};Bh(lc,n),_r(s=>{s==="beforeUnmount"?n.value=[]:o()})};let Do=null,jn=null;const Cg={wait:()=>Do,pending:()=>{Do=new Promise(e=>{jn=e})},resolve:()=>{jn==null||jn(),Do=null,jn=null}},cc=()=>Cg,Rn=(e,t)=>{const{notFound:n,meta:r,path:o}=dn(e,t);return n?{text:o,link:o}:{text:r.title||o,link:o}},An=(e="",t="")=>Xl(t)||gr(t)?t:`${dd(e)}${t}`,Tg=e=>({text:e.title,link:e.link,children:Hs(e.children)}),Hs=e=>e?e.map(t=>Tg(t)):[],uc=(e,t)=>[{text:e.title,children:Hs(t)}],fc=(e,t,n,r="")=>{const o=(s,i)=>{var l;const a=pt(s)?Rn(An(i,s)):pt(s.link)?{...s,link:ec(s.link)?Rn(An(i,s.link)).link:s.link}:s;if("children"in a)return{...a,children:a.children.map(c=>o(c,An(i,a.prefix)))};if(a.link===n){const c=((l=t[0])==null?void 0:l.level)===1?t[0].children:t;return{...a,children:Hs(c)}}return a};return e.map(s=>o(s,r))},xg=(e,t,n,r)=>{const o=wp(e).sort((s,i)=>i.length-s.length);for(const s of o)if(Ql(decodeURI(r),s)){const i=e[s];return i?i==="heading"?uc(t,n):fc(i,n,r,s):[]}return console.warn(`${decodeURI(r)} is missing sidebar config.`),[]},dc=Symbol("sidebarItems"),Ms=()=>{const e=Fe(dc);if(!e)throw new Error("useSidebarItems() is called without provider.");return e},Ag=(e,t,n,r,o)=>e===!1?[]:e==="heading"?uc(t,o):Array.isArray(e)?fc(e,o,n,r):_s(e)?xg(e,t,o,n):[],kg=()=>{const{frontmatter:e,page:t,routeLocale:n,themeLocale:r}=Ie(),o=wg(),s=uo(),i=O(()=>e.value.home?!1:e.value.sidebar??r.value.sidebar??"heading"),a=O(()=>Ag(i.value,t.value,s.value,n.value,o.value));Kt(dc,a)},Lg=me({__name:"Badge",props:{type:{default:"tip"},text:{default:""},vertical:{default:void 0}},setup(e){return(t,n)=>(T(),F("span",{class:je(["vp-badge",t.type]),style:Yt({verticalAlign:t.vertical})},[Ae(t.$slots,"default",{},()=>[ot(le(t.text),1)])],6))}}),Pg=me({__name:"VPFadeSlideYTransition",emits:["beforeEnter","beforeLeave"],setup(e){return(t,n)=>(T(),ge(Cl,{name:"vp-fade-slide-y",mode:"out-in",onBeforeEnter:n[0]||(n[0]=r=>t.$emit("beforeEnter")),onBeforeLeave:n[1]||(n[1]=r=>t.$emit("beforeLeave"))},{default:pe(()=>[Ae(t.$slots,"default")]),_:3}))}}),Rg={key:0,class:"vp-features"},$g=me({__name:"VPHomeFeatures",setup(e){const{frontmatter:t}=Ie(),n=O(()=>t.value.features??[]);return(r,o)=>n.value.length?(T(),F("div",Rg,[(T(!0),F(ve,null,rt(n.value,s=>(T(),F("div",{key:s.title,class:"vp-feature"},[j("h2",null,le(s.title),1),j("p",null,le(s.details),1)]))),128))])):ue("",!0)}}),Og=["innerHTML"],Ig=["textContent"],Hg=me({__name:"VPHomeFooter",setup(e){const t=In(),n=O(()=>t.value.footer),r=O(()=>t.value.footerHtml);return(o,s)=>n.value?(T(),F(ve,{key:0},[r.value?(T(),F("div",{key:0,class:"vp-footer","vp-footer":"",innerHTML:n.value},null,8,Og)):(T(),F("div",{key:1,class:"vp-footer","vp-footer":"",textContent:le(n.value)},null,8,Ig))],64)):ue("",!0)}}),It=me({__name:"VPAutoLink",props:{config:{}},setup(e){return(t,n)=>(T(),ge(B(kh),{config:t.config},Nu({before:pe(()=>[Ae(t.$slots,"before",vo(Ir(t.config)))]),after:pe(()=>[Ae(t.$slots,"after",vo(Ir(t.config)))]),_:2},[t.$slots.default?{name:"default",fn:pe(()=>[Ae(t.$slots,"default",vo(Ir(t.config)))]),key:"0"}:void 0]),1032,["config"]))}}),Mg={class:"vp-hero"},Dg={key:0,id:"main-title"},Ng={key:1,class:"vp-hero-description"},Bg={key:2,class:"vp-hero-actions"},jg=me({__name:"VPHomeHero",setup(e){const{frontmatter:t,siteLocale:n}=Ie(),r=Is(),o=O(()=>t.value.heroText===null?null:t.value.heroText||n.value.title||"Hello"),s=O(()=>t.value.tagline===null?null:t.value.tagline||n.value.description||"Welcome to your VuePress site"),i=O(()=>r.value&&t.value.heroImageDark!==void 0?t.value.heroImageDark:t.value.heroImage),a=O(()=>t.value.heroAlt||o.value||"hero"),l=O(()=>t.value.heroHeight??280),c=O(()=>Array.isArray(t.value.actions)?t.value.actions.map(({type:f="primary",...p})=>({type:f,...p})):[]),u=()=>{if(!i.value)return null;const f=te("img",{class:"vp-hero-image",src:xs(i.value),alt:a.value,height:l.value});return t.value.heroImageDark===void 0?f:te(Cs,()=>f)};return(f,p)=>(T(),F("header",Mg,[J(u),o.value?(T(),F("h1",Dg,le(o.value),1)):ue("",!0),s.value?(T(),F("p",Ng,le(s.value),1)):ue("",!0),c.value.length?(T(),F("p",Bg,[(T(!0),F(ve,null,rt(c.value,g=>(T(),ge(It,{key:g.text,class:je(["vp-hero-action-button",[g.type]]),config:g},null,8,["class","config"]))),128))])):ue("",!0)]))}}),Fg={class:"vp-home"},Vg={"vp-content":""},zg=me({__name:"VPHome",setup(e){return(t,n)=>(T(),F("main",Fg,[J(jg),J($g),j("div",Vg,[J(B(Ts))]),J(Hg)]))}}),Ug=["aria-hidden"],Wg=me({__name:"VPNavbarBrand",setup(e){const{routeLocale:t,siteLocale:n,themeLocale:r}=Ie(),o=Is(),s=O(()=>r.value.home||t.value),i=O(()=>n.value.title),a=O(()=>o.value&&r.value.logoDark!==void 0?r.value.logoDark:r.value.logo),l=O(()=>r.value.logoAlt??i.value),c=O(()=>i.value.toLocaleUpperCase().trim()===l.value.toLocaleUpperCase().trim()),u=()=>{if(!a.value)return null;const f=te("img",{class:"vp-site-logo",src:xs(a.value),alt:l.value});return r.value.logoDark===void 0?f:te(Cs,()=>f)};return(f,p)=>(T(),ge(B(Ct),{to:s.value},{default:pe(()=>[J(u),i.value?(T(),F("span",{key:0,class:je(["vp-site-name",{"vp-hide-mobile":a.value}]),"aria-hidden":c.value},le(i.value),11,Ug)):ue("",!0)]),_:1},8,["to"]))}}),hc=me({__name:"VPDropdownTransition",setup(e){const t=r=>{r.style.height=`${r.scrollHeight}px`},n=r=>{r.style.height=""};return(r,o)=>(T(),ge(Cl,{name:"vp-dropdown",onEnter:t,onAfterEnter:n,onBeforeLeave:t},{default:pe(()=>[Ae(r.$slots,"default")]),_:3}))}}),qg=["aria-label"],Kg={class:"title"},Gg=["aria-label"],Zg={class:"title"},Jg={class:"vp-navbar-dropdown"},Yg={class:"vp-navbar-dropdown-subtitle"},Qg={key:1},Xg={class:"vp-navbar-dropdown-subitem-wrapper"},em=me({__name:"VPNavbarDropdown",props:{config:{}},setup(e){const t=e,{config:n}=Oa(t),[r,o]=Ls(),s=O(()=>n.value.ariaLabel||n.value.text),i=(l,c)=>c[c.length-1]===l,a=l=>{l.detail===0?o():o(!1)};return _r(()=>{o(!1)}),(l,c)=>(T(),F("div",{class:je(["vp-navbar-dropdown-wrapper",{open:B(r)}])},[j("button",{class:"vp-navbar-dropdown-title",type:"button","aria-label":s.value,onClick:a},[j("span",Kg,le(B(n).text),1),c[1]||(c[1]=j("span",{class:"arrow down"},null,-1))],8,qg),j("button",{class:"vp-navbar-dropdown-title-mobile",type:"button","aria-label":s.value,onClick:c[0]||(c[0]=()=>B(o)())},[j("span",Zg,le(B(n).text),1),j("span",{class:je(["arrow",B(r)?"down":"right"])},null,2)],8,Gg),J(hc,null,{default:pe(()=>[Ur(j("ul",Jg,[(T(!0),F(ve,null,rt(B(n).children,u=>(T(),F("li",{key:u.text,class:"vp-navbar-dropdown-item"},["children"in u?(T(),F(ve,{key:0},[j("h4",Yg,[u.link?(T(),ge(It,{key:0,config:u,onFocusout:()=>{i(u,B(n).children)&&u.children.length===0&&(r.value=!1)}},null,8,["config","onFocusout"])):(T(),F("span",Qg,le(u.text),1))]),j("ul",Xg,[(T(!0),F(ve,null,rt(u.children,f=>(T(),F("li",{key:f.link,class:"vp-navbar-dropdown-subitem"},[J(It,{config:f,onFocusout:()=>{i(f,u.children)&&i(u,B(n).children)&&B(o)(!1)}},null,8,["config","onFocusout"])]))),128))])],64)):(T(),ge(It,{key:1,config:u,onFocusout:()=>{i(u,B(n).children)&&B(o)(!1)}},null,8,["config","onFocusout"]))]))),128))],512),[[Gr,B(r)]])]),_:1})],2))}}),pc=(e,t="")=>pt(e)?Rn(An(t,e)):"children"in e?{...e,children:e.children.map(n=>pc(n,An(t,e.prefix)))}:{...e,link:ec(e.link)?Rn(An(t,e.link)).link:e.link},tm=()=>{const{themeLocale:e}=Ie();return O(()=>(e.value.navbar||[]).map(t=>pc(t)))},gc=e=>!mr(e)||e.includes("github.com")?"GitHub":e.includes("bitbucket.org")?"Bitbucket":e.includes("gitlab.com")?"GitLab":e.includes("gitee.com")?"Gitee":null,nm=()=>{const{themeLocale:e}=Ie(),t=O(()=>e.value.repo),n=O(()=>t.value?gc(t.value):null),r=O(()=>t.value&&!mr(t.value)?`https://github.com/${t.value}`:t.value),o=O(()=>r.value?e.value.repoLabel?e.value.repoLabel:n.value===null?"Source":n.value:null);return O(()=>!r.value||!o.value?[]:[{text:o.value,link:r.value}])},rm=()=>{const e=On(),t=bp(),{routeLocale:n,site:r,siteLocale:o,theme:s,themeLocale:i}=Ie();return O(()=>{const a=Object.keys(r.value.locales);if(a.length<2)return[];const l=e.path,c=e.fullPath;return[{text:`${i.value.selectLanguageText}`,ariaLabel:`${i.value.selectLanguageAriaLabel??i.value.selectLanguageText}`,children:a.map(f=>{var A,k;const p=((A=r.value.locales)==null?void 0:A[f])??{},g=((k=s.value.locales)==null?void 0:k[f])??{},_=`${p.lang}`,b=g.selectLanguageName??_;if(_===o.value.lang)return{text:b,activeMatch:".",link:e.fullPath};const w=l.replace(n.value,f);return{text:b,link:t.value.some(m=>m===w)?c.replace(l,w):g.home??f}})}]})},om="719px",sm={mobile:om};var sr;(function(e){e.Mobile="mobile"})(sr||(sr={}));const im={[sr.Mobile]:Number.parseInt(sm.mobile.replace("px",""),10)},mc=(e,t)=>{const n=im[e];Number.isInteger(n)&&(ft("orientationchange",()=>{t(n)},!1),ft("resize",()=>{t(n)},!1),st(()=>{t(n)}))},am=["aria-label"],vc=me({__name:"VPNavbarItems",setup(e){const{themeLocale:t}=Ie(),n=tm(),r=rm(),o=nm(),s=qe(!1),i=O(()=>t.value.navbarLabel??"site navigation"),a=O(()=>[...n.value,...r.value,...o.value]);return mc(sr.Mobile,l=>{s.value=window.innerWidth<l}),(l,c)=>a.value.length?(T(),F("nav",{key:0,class:"vp-navbar-items","aria-label":i.value},[(T(!0),F(ve,null,rt(a.value,u=>(T(),F("div",{key:u.text,class:"vp-navbar-item"},["children"in u?(T(),ge(em,{key:0,class:je({mobile:s.value}),config:u},null,8,["class","config"])):(T(),ge(It,{key:1,config:u},null,8,["config"]))]))),128))],8,am)):ue("",!0)}}),Tt=(e,t)=>{const n=e.__vccOpts||e;for(const[r,o]of t)n[r]=o;return n},lm={},cm={class:"dark-icon",viewBox:"0 0 32 32"};function um(e,t){return T(),F("svg",cm,t[0]||(t[0]=[j("path",{d:"M13.502 5.414a15.075 15.075 0 0 0 11.594 18.194a11.113 11.113 0 0 1-7.975 3.39c-.138 0-.278.005-.418 0a11.094 11.094 0 0 1-3.2-21.584M14.98 3a1.002 1.002 0 0 0-.175.016a13.096 13.096 0 0 0 1.825 25.981c.164.006.328 0 .49 0a13.072 13.072 0 0 0 10.703-5.555a1.01 1.01 0 0 0-.783-1.565A13.08 13.08 0 0 1 15.89 4.38A1.015 1.015 0 0 0 14.98 3z",fill:"currentColor"},null,-1)]))}const fm=Tt(lm,[["render",um]]),dm={},hm={class:"light-icon",viewBox:"0 0 32 32"};function pm(e,t){return T(),F("svg",hm,t[0]||(t[0]=[so('<path d="M16 12.005a4 4 0 1 1-4 4a4.005 4.005 0 0 1 4-4m0-2a6 6 0 1 0 6 6a6 6 0 0 0-6-6z" fill="currentColor"></path><path d="M5.394 6.813l1.414-1.415l3.506 3.506L8.9 10.318z" fill="currentColor"></path><path d="M2 15.005h5v2H2z" fill="currentColor"></path><path d="M5.394 25.197L8.9 21.691l1.414 1.415l-3.506 3.505z" fill="currentColor"></path><path d="M15 25.005h2v5h-2z" fill="currentColor"></path><path d="M21.687 23.106l1.414-1.415l3.506 3.506l-1.414 1.414z" fill="currentColor"></path><path d="M25 15.005h5v2h-5z" fill="currentColor"></path><path d="M21.687 8.904l3.506-3.506l1.414 1.415l-3.506 3.505z" fill="currentColor"></path><path d="M15 2.005h2v5h-2z" fill="currentColor"></path>',9)]))}const gm=Tt(dm,[["render",pm]]),mm=["title"],vm=me({__name:"VPToggleColorModeButton",setup(e){const{themeLocale:t}=Ie(),n=Is(),r=()=>{n.value=!n.value};return(o,s)=>(T(),F("button",{type:"button",class:"vp-toggle-color-mode-button",title:B(t).toggleColorMode,onClick:r},[Ur(J(gm,null,null,512),[[Gr,!B(n)]]),Ur(J(fm,null,null,512),[[Gr,B(n)]])],8,mm))}}),_m=["title"],ym=me({__name:"VPToggleSidebarButton",emits:["toggle"],setup(e){const{themeLocale:t}=Ie();return(n,r)=>(T(),F("div",{class:"vp-toggle-sidebar-button",title:B(t).toggleSidebar,"aria-expanded":"false",role:"button",tabindex:"0",onClick:r[0]||(r[0]=o=>n.$emit("toggle"))},r[1]||(r[1]=[j("div",{class:"icon","aria-hidden":"true"},[j("span"),j("span"),j("span")],-1)]),8,_m))}}),bm={ref:"navbar-brand"},Em=me({__name:"VPNavbar",emits:["toggleSidebar"],setup(e){const t=Jl("SearchBox")?Ga("SearchBox"):()=>null,{themeLocale:n}=Ie(),r=Us("navbar"),o=Us("navbar-brand"),s=qe(0),i=O(()=>s.value?{maxWidth:`${s.value}px`}:{}),a=(l,c)=>{var p;const u=(p=l==null?void 0:l.ownerDocument.defaultView)==null?void 0:p.getComputedStyle(l,null)[c],f=Number.parseInt(u,10);return Number.isNaN(f)?0:f};return mc(sr.Mobile,l=>{var u;const c=a(r.value,"paddingLeft")+a(r.value,"paddingRight");window.innerWidth<l?s.value=0:s.value=r.value.offsetWidth-c-(((u=o.value)==null?void 0:u.offsetWidth)??0)}),(l,c)=>(T(),F("header",{ref_key:"navbar",ref:r,class:"vp-navbar","vp-navbar":""},[J(ym,{onToggle:c[0]||(c[0]=u=>l.$emit("toggleSidebar"))}),j("span",bm,[J(Wg)],512),j("div",{class:"vp-navbar-items-wrapper",style:Yt(i.value)},[Ae(l.$slots,"before"),J(vc,{class:"vp-hide-mobile"}),Ae(l.$slots,"after"),B(n).colorModeSwitch?(T(),ge(vm,{key:0})):ue("",!0),J(B(t))],4)],512))}}),wm={},Sm={class:"edit-icon",viewBox:"0 0 1024 1024"};function Cm(e,t){return T(),F("svg",Sm,t[0]||(t[0]=[j("g",{fill:"currentColor"},[j("path",{d:"M430.818 653.65a60.46 60.46 0 0 1-50.96-93.281l71.69-114.012 7.773-10.365L816.038 80.138A60.46 60.46 0 0 1 859.225 62a60.46 60.46 0 0 1 43.186 18.138l43.186 43.186a60.46 60.46 0 0 1 0 86.373L588.879 565.55l-8.637 8.637-117.466 68.234a60.46 60.46 0 0 1-31.958 11.229z"}),j("path",{d:"M728.802 962H252.891A190.883 190.883 0 0 1 62.008 771.98V296.934a190.883 190.883 0 0 1 190.883-192.61h267.754a60.46 60.46 0 0 1 0 120.92H252.891a69.962 69.962 0 0 0-69.098 69.099V771.98a69.962 69.962 0 0 0 69.098 69.098h475.911A69.962 69.962 0 0 0 797.9 771.98V503.363a60.46 60.46 0 1 1 120.922 0V771.98A190.883 190.883 0 0 1 728.802 962z"})],-1)]))}const Tm=Tt(wm,[["render",Cm]]),xm={GitHub:":repo/edit/:branch/:path",GitLab:":repo/-/edit/:branch/:path",Gitee:":repo/edit/:branch/:path",Bitbucket:":repo/src/:branch/:path?mode=edit&spa=0&at=:branch&fileviewer=file-view-default"},Am=({docsRepo:e,editLinkPattern:t})=>{if(t)return t;const n=gc(e);return n!==null?xm[n]:null},km=({docsRepo:e,docsBranch:t,docsDir:n,filePathRelative:r,editLinkPattern:o})=>{if(!r)return null;const s=Am({docsRepo:e,editLinkPattern:o});return s?s.replace(/:repo/,mr(e)?e:`https://github.com/${e}`).replace(/:branch/,t).replace(/:path/,kl(`${Al(n)}/${r}`)):null},Lm=()=>{const{frontmatter:e,page:t,themeLocale:n}=Ie();return O(()=>{if(!(e.value.editLink??n.value.editLink??!0))return null;const{repo:o,docsRepo:s=o,docsBranch:i="main",docsDir:a="",editLinkText:l}=n.value;if(!s)return null;const c=km({docsRepo:s,docsBranch:i,docsDir:a,filePathRelative:t.value.filePathRelative,editLinkPattern:e.value.editLinkPattern??n.value.editLinkPattern});return c?{text:l??"Edit this page",link:c}:null})},Pm={class:"vp-page-meta"},Rm={key:0,class:"vp-meta-item edit-link"},$m={class:"vp-meta-item git-info"},Om={key:0,class:"vp-meta-item last-updated"},Im={class:"meta-item-label"},Hm=["datetime"],Mm={key:1,class:"vp-meta-item contributors"},Dm={class:"meta-item-label"},Nm={class:"meta-item-info"},Bm=["title"],jm=me({__name:"VPPageMeta",setup(e){const{frontmatter:t,themeLocale:n}=Ie(),r=tc(()=>t.value.contributors??n.value.contributors??!0),o=Lm(),s=nc(()=>t.value.lastUpdated??n.value.lastUpdated??!0);return(i,a)=>(T(),F("footer",Pm,[B(o)?(T(),F("div",Rm,[J(It,{class:"label",config:B(o)},{before:pe(()=>[J(Tm)]),_:1},8,["config"])])):ue("",!0),j("div",$m,[B(s)?(T(),F("div",Om,[j("span",Im,le(B(n).lastUpdatedText??B(s).locale)+": ",1),j("time",{class:"meta-item-info",datetime:B(s).iso,"data-allow-mismatch":""},le(B(s).text),9,Hm)])):ue("",!0),B(r).length?(T(),F("div",Mm,[j("span",Dm,le(B(n).contributorsText)+": ",1),j("span",Nm,[(T(!0),F(ve,null,rt(B(r),(l,c)=>(T(),F(ve,{key:c},[j("span",{class:"contributor",title:`email: ${l.email}`},le(l.name),9,Bm),c!==B(r).length-1?(T(),F(ve,{key:0},[ot(", ")],64)):ue("",!0)],64))),128))])])):ue("",!0)])]))}}),Fm=()=>{const e=vr(),t=On();return n=>{n&&(Xl(n)?t.fullPath!==n&&e.push(n):gr(n)?window.open(n):e.push(encodeURI(n)))}},ra=(e,t)=>e===!1?!1:pt(e)?Rn(e,t):_s(e)?{...e,link:Rn(e.link,t).link}:null,es=(e,t,n)=>{const r=e.findIndex(s=>s.link===t);if(r!==-1){const s=e[r+n];return s?s.link?s:"prefix"in s&&!dn(s.prefix).notFound?{...s,link:s.prefix}:null:null}for(const s of e)if("children"in s){const i=es(s.children,t,n);if(i)return i}const o=e.findIndex(s=>"prefix"in s&&s.prefix===t);if(o!==-1){const s=e[o+n];return s?s.link?s:"prefix"in s&&!dn(s.prefix).notFound?{...s,link:s.prefix}:null:null}return null},Vm=()=>{const{frontmatter:e,themeLocale:t}=Ie(),n=Ms(),r=uo(),o=O(()=>{const i=ra(e.value.prev,r.value);return i===!1?null:i??(t.value.prev===!1?null:es(n.value,r.value,-1))}),s=O(()=>{const i=ra(e.value.next,r.value);return i===!1?null:i??(t.value.next===!1?null:es(n.value,r.value,1))});return{prevLink:o,nextLink:s}},zm=["aria-label"],Um={class:"hint"},Wm={class:"link"},qm={class:"external-link"},Km={class:"hint"},Gm={class:"link"},Zm={class:"external-link"},Jm=me({__name:"VPPageNav",setup(e){const{themeLocale:t}=Ie(),n=Fm(),{prevLink:r,nextLink:o}=Vm(),s=O(()=>t.value.pageNavbarLabel??"page navigation");return ft("keydown",i=>{i.altKey&&(i.key==="ArrowRight"?o.value&&(n(o.value.link),i.preventDefault()):i.key==="ArrowLeft"&&r.value&&(n(r.value.link),i.preventDefault()))}),(i,a)=>B(r)||B(o)?(T(),F("nav",{key:0,class:"vp-page-nav","aria-label":s.value},[B(r)?(T(),ge(It,{key:0,class:"prev",config:B(r)},{default:pe(()=>[j("div",Um,[a[0]||(a[0]=j("span",{class:"arrow left"},null,-1)),ot(" "+le(B(t).prev??"Prev"),1)]),j("div",Wm,[j("span",qm,le(B(r).text),1)])]),_:1},8,["config"])):ue("",!0),B(o)?(T(),ge(It,{key:1,class:"next",config:B(o)},{default:pe(()=>[j("div",Km,[ot(le(B(t).next??"Next")+" ",1),a[1]||(a[1]=j("span",{class:"arrow right"},null,-1))]),j("div",Gm,[j("span",Zm,le(B(o).text),1)])]),_:1},8,["config"])):ue("",!0)],8,zm)):ue("",!0)}}),Ym={class:"vp-page"},Qm={"vp-content":""},Xm=me({__name:"VPPage",setup(e){return(t,n)=>(T(),F("main",Ym,[Ae(t.$slots,"top"),j("div",Qm,[Ae(t.$slots,"content-top"),J(B(Ts)),Ae(t.$slots,"content-bottom")]),J(jm),J(Jm),Ae(t.$slots,"bottom")]))}}),oa=e=>decodeURI(e).replace(/#.*$/,"").replace(/(index)?\.(md|html)$/,""),ev=(e,t)=>{if(t.hash===e)return!0;const n=oa(t.path),r=oa(e);return n===r},_c=(e,t)=>e.link&&ev(e.link,t)?!0:"children"in e?e.children.some(n=>_c(n,t)):!1,tv={class:"vp-sidebar-children"},nv=me({__name:"VPSidebarItem",props:{item:{},depth:{default:0}},setup(e){const t=e,{item:n,depth:r}=Oa(t),o=On(),s=vr(),i=O(()=>n.value.collapsible),a=O(()=>_c(n.value,o)),l=O(()=>({"vp-sidebar-item":!0,"vp-sidebar-heading":r.value===0,active:a.value,collapsible:i.value})),c=O(()=>i.value?a.value:!0),[u,f]=Ls(c.value),p=_=>{i.value&&(_.preventDefault(),f())},g=s.afterEach(()=>{fr(()=>{u.value=c.value})});return hs(()=>{g()}),(_,b)=>{const w=Ga("VPSidebarItem",!0);return T(),F("li",null,[B(n).link?(T(),ge(It,{key:0,class:je(l.value),config:B(n)},{after:pe(()=>[i.value?(T(),F("span",{key:0,class:je(["arrow",B(u)?"down":"right"])},null,2)):ue("",!0)]),_:1},8,["class","config"])):(T(),F("p",{key:1,tabindex:"0",class:je(l.value),onClick:p,onKeydown:Jf(p,["enter"])},[ot(le(B(n).text)+" ",1),i.value?(T(),F("span",{key:0,class:je(["arrow",B(u)?"down":"right"])},null,2)):ue("",!0)],34)),"children"in B(n)&&B(n).children.length?(T(),ge(hc,{key:2},{default:pe(()=>[Ur(j("ul",tv,[(T(!0),F(ve,null,rt(B(n).children,A=>(T(),ge(w,{key:`${B(r)}${A.text}${A.link}`,item:A,depth:B(r)+1},null,8,["item","depth"]))),128))],512),[[Gr,B(u)]])]),_:1})):ue("",!0)])}}}),rv={key:0,class:"vp-sidebar-items"},ov=me({__name:"VPSidebarItems",setup(e){const t=On(),n=Ms();return st(()=>{ut(()=>t.hash,r=>{const o=document.querySelector(".vp-sidebar");if(!o)return;const s=document.querySelector(`.vp-sidebar .vp-sidebar-item.auto-link[href="${t.path}${r}"]`);if(!s)return;const{top:i,height:a}=o.getBoundingClientRect(),{top:l,height:c}=s.getBoundingClientRect();l<i?s.scrollIntoView(!0):l+c>i+a&&s.scrollIntoView(!1)})}),(r,o)=>B(n).length?(T(),F("ul",rv,[(T(!0),F(ve,null,rt(B(n),s=>(T(),ge(nv,{key:`${s.text}${s.link}`,item:s},null,8,["item"]))),128))])):ue("",!0)}}),sv={class:"vp-sidebar","vp-sidebar":""},iv=me({__name:"VPSidebar",setup(e){return(t,n)=>(T(),F("aside",sv,[J(vc),Ae(t.$slots,"top"),J(ov),Ae(t.$slots,"bottom")]))}}),yr=me({__name:"Layout",setup(e){const{frontmatter:t,page:n,themeLocale:r}=Ie(),o=O(()=>t.value.navbar??r.value.navbar??!0),s=Ms(),i=qe(!1),a=w=>{i.value=typeof w=="boolean"?w:!i.value},l={x:0,y:0},c=w=>{l.x=w.changedTouches[0].clientX,l.y=w.changedTouches[0].clientY},u=w=>{const A=w.changedTouches[0].clientX-l.x,k=w.changedTouches[0].clientY-l.y;Math.abs(A)>Math.abs(k)&&Math.abs(A)>40&&(A>0&&l.x<=80?a(!0):a(!1))},f=O(()=>t.value.externalLinkIcon??r.value.externalLinkIcon??!0),p=O(()=>[{"no-navbar":!o.value,"no-sidebar":!s.value.length,"sidebar-open":i.value,"external-link-icon":f.value},t.value.pageClass]);_r(()=>{a(!1)});const g=cc(),_=g.resolve,b=g.pending;return(w,A)=>(T(),F("div",{class:je(["vp-theme-container",p.value]),"vp-container":"",onTouchstart:c,onTouchend:u},[Ae(w.$slots,"navbar",{},()=>[o.value?(T(),ge(Em,{key:0,onToggleSidebar:a},{before:pe(()=>[Ae(w.$slots,"navbar-before")]),after:pe(()=>[Ae(w.$slots,"navbar-after")]),_:3})):ue("",!0)]),j("div",{class:"vp-sidebar-mask",onClick:A[0]||(A[0]=k=>a(!1))}),Ae(w.$slots,"sidebar",{},()=>[J(iv,null,{top:pe(()=>[Ae(w.$slots,"sidebar-top")]),bottom:pe(()=>[Ae(w.$slots,"sidebar-bottom")]),_:3})]),Ae(w.$slots,"page",{},()=>[J(Pg,{onBeforeEnter:B(_),onBeforeLeave:B(b)},{default:pe(()=>[B(t).home?(T(),ge(zg,{key:0})):(T(),ge(Xm,{key:B(n).path},{top:pe(()=>[Ae(w.$slots,"page-top")]),"content-top":pe(()=>[Ae(w.$slots,"page-content-top")]),"content-bottom":pe(()=>[Ae(w.$slots,"page-content-bottom")]),bottom:pe(()=>[Ae(w.$slots,"page-bottom")]),_:3}))]),_:3},8,["onBeforeEnter","onBeforeLeave"])])],34))}}),av={class:"vp-theme-container","vp-container":""},lv={class:"page"},cv={"vp-content":""},uv=me({__name:"NotFound",setup(e){const{routeLocale:t,themeLocale:n}=Ie(),r=O(()=>n.value.notFound??["Not Found"]),o=()=>r.value[Math.floor(Math.random()*r.value.length)],s=O(()=>n.value.home??t.value),i=O(()=>n.value.backToHome??"Back to home");return(a,l)=>(T(),F("div",av,[j("main",lv,[j("div",cv,[l[0]||(l[0]=j("h1",null,"404",-1)),j("blockquote",null,le(o()),1),J(B(Ct),{to:s.value},{default:pe(()=>[ot(le(i.value),1)]),_:1},8,["to"])])])]))}}),fv=Tt(uv,[["__scopeId","data-v-5e9d2163"]]),dv=Qt({enhance({app:e,router:t}){Jl("Badge")||e.component("Badge",Lg);const n=t.options.scrollBehavior;t.options.scrollBehavior=async(...r)=>(await cc().wait(),n(...r))},setup(){Eg(),Sg(),kg()},layouts:{Layout:yr,NotFound:fv}}),hv=Object.freeze(Object.defineProperty({__proto__:null,default:dv},Symbol.toStringTag,{value:"Module"})),yc=JSON.parse('{"category":{"/":{"path":"/category/","map":{"History":{"path":"/category/history/","indexes":[0,1]},"Category A":{"path":"/category/category-a/","indexes":[2,3,4,5,6,7,8,9,10,11,12,13]},"Category B":{"path":"/category/category-b/","indexes":[2,3,4,5,6,7,8,10,11,12]},"Category C":{"path":"/category/category-c/","indexes":[14,15]}}}},"tag":{"/":{"path":"/tag/","map":{"WWI":{"path":"/tag/wwi/","indexes":[1]},"WWII":{"path":"/tag/wwii/","indexes":[0]},"tag A":{"path":"/tag/tag-a/","indexes":[5,6,7,8,9,13]},"tag B":{"path":"/tag/tag-b/","indexes":[5,6,7,8,9,13]},"tag C":{"path":"/tag/tag-c/","indexes":[2,3,4,10,11,12]},"tag D":{"path":"/tag/tag-d/","indexes":[2,3,4,10,11,12]},"tag E":{"path":"/tag/tag-e/","indexes":[14,15]}}}}}');import.meta.webpackHot&&(import.meta.webpackHot.accept(),__VUE_HMR_RUNTIME__.updateBlogCategory&&__VUE_HMR_RUNTIME__.updateBlogCategory(yc));const bc=JSON.parse('["/posts/archive2.html","/posts/archive1.html","/posts/article9.html","/posts/article8.html","/posts/article7.html","/posts/article6.html","/posts/article5.html","/posts/article4.html","/posts/article3.html","/posts/article2.html","/posts/article12.html","/posts/article11.html","/posts/article10.html","/posts/article1.html","/posts/sticky2.html","/posts/sticky.html"]'),Ec=JSON.parse('{"article":{"/":{"path":"/article/","indexes":[14,15,10,11,12,2,3,4,5,6,7,8,9,13]}},"timeline":{"/":{"path":"/timeline/","indexes":[10,11,12,2,3,4,5,6,7,8,9,13,15,14,0,1]}}}');import.meta.webpackHot&&(import.meta.webpackHot.accept(),__VUE_HMR_RUNTIME__.updateBlogType&&__VUE_HMR_RUNTIME__.updateBlogType(Ec));const ts=Pe(yc);$n(ts);const wc=e=>{const t=co(),n=In(),r=Ss();return O(()=>{var a;const o=e??((a=n.value.blog)==null?void 0:a.key)??"";if(!o)return console.warn("useBlogCategory: key not found"),{path:"/",map:{}};if(!(o in ts.value))throw new Error(`useBlogCategory: key ${o} is invalid`);const s=ts.value[o][r.value],i={path:s.path,map:{}};for(const l in s.map){const c=s.map[l];i.map[l]={path:c.path,items:[]};for(const u of c.indexes){const{path:f,meta:p}=dn(bc[u]);i.map[l].items.push({path:f,info:p._blog})}t.value.path===c.path&&(i.currentItems=i.map[l].items)}return i})},ns=Pe(Ec);$n(ns);const Sc=e=>{const t=In(),n=Ss();return O(()=>{var i;const r=e??((i=t.value.blog)==null?void 0:i.key)??"";if(!r)return console.warn("useBlogType: key not found"),{path:"/",items:[]};if(!(r in ns.value))throw new Error(`useBlogType: key ${e} is invalid`);const o=ns.value[r][n.value],s={path:o.path,items:[]};for(const a of o.indexes){const{path:l,meta:c}=dn(bc[a]);s.items.push({path:l,info:c._blog})}return s})},pv={class:"article-wrapper"},gv={key:0},mv=["onClick"],vv={class:"title"},_v={class:"article-info"},yv={key:0,class:"author"},bv={key:1,class:"date"},Ev={key:2,class:"category"},wv={key:3,class:"tag"},Sv=["innerHTML"],fo={__name:"ArticleList",props:{items:{type:Array,required:!0},isTimeline:Boolean},setup(e){return(t,n)=>(T(),F("div",pv,[e.items.length?ue("",!0):(T(),F("div",gv,"Nothing in here.")),(T(!0),F(ve,null,rt(e.items,({info:r,path:o})=>(T(),F("article",{key:o,class:"article",onClick:s=>t.$router.push(o)},[j("header",vv,le((e.isTimeline?`${new Date(r.date).toLocaleDateString()}: `:"")+r.title),1),n[0]||(n[0]=j("hr",null,null,-1)),j("div",_v,[r.author?(T(),F("span",yv,"Author: "+le(r.author),1)):ue("",!0),r.date&&!e.isTimeline?(T(),F("span",bv,"Date: "+le(new Date(r.date).toLocaleDateString()),1)):ue("",!0),r.category?(T(),F("span",Ev,"Category: "+le(r.category.join(", ")),1)):ue("",!0),r.tag?(T(),F("span",wv,"Tag: "+le(r.tag.join(", ")),1)):ue("",!0)]),r.excerpt?(T(),F("div",{key:0,class:"excerpt",innerHTML:r.excerpt},null,8,Sv)):ue("",!0)],8,mv))),128))]))}},Cv={class:"page"},Tv={__name:"Article",setup(e){const t=Sc("article");return(n,r)=>(T(),ge(yr,null,{page:pe(()=>[j("main",Cv,[J(fo,{items:B(t).items},null,8,["items"])])]),_:1}))}},xv={class:"page"},Av={class:"category-wrapper"},kv={class:"category-num"},Lv={__name:"Category",setup(e){const t=wc("category"),n=uo();return(r,o)=>(T(),ge(yr,null,{page:pe(()=>[j("main",xv,[j("div",Av,[(T(!0),F(ve,null,rt(B(t).map,({items:s,path:i},a)=>(T(),ge(B(Ct),{key:a,to:i,active:B(n)===i,class:"category"},{default:pe(()=>[ot(le(a)+" ",1),j("span",kv,le(s.length),1)]),_:2},1032,["to","active"]))),128))]),J(fo,{items:B(t).currentItems??[]},null,8,["items"])])]),_:1}))}},Pv={class:"mobile-footer"},Rv={class:"footer-content"},$v={key:0,class:"footer-actions"},Ov={key:1,class:"footer-actions"},Iv={key:2,class:"footer-links"},Hv={class:"footer-copyright"},Mv={class:"copyright-text"},Dv={key:0,class:"company-info"},Nv={__name:"MobileFooter",props:{showBackToTop:{type:Boolean,default:!0},showMoreContent:{type:Boolean,default:!1},moreContentUrl:{type:String,default:"/articles/"},quickLinks:{type:Array,default:()=>[]},copyrightText:{type:String,default:"© 2022-2023 SPINRED 版权所有"},companyInfo:{type:String,default:"无锡市双马智能科技有限公司"}},setup(e){const t=e,n=qe(!1),r=()=>{window.scrollTo({top:0,behavior:"smooth"})},o=()=>{n.value=window.scrollY>300};return st(()=>{t.showBackToTop&&(window.addEventListener("scroll",o),o())}),hr(()=>{t.showBackToTop&&window.removeEventListener("scroll",o)}),(s,i)=>(T(),F("footer",Pv,[j("div",Rv,[e.showBackToTop?(T(),F("div",$v,[j("button",{class:je(["back-to-top-btn",{visible:n.value}]),onClick:r}," ↑ 返回顶部 ",2)])):ue("",!0),e.showMoreContent?(T(),F("div",Ov,[J(B(Ct),{to:e.moreContentUrl,class:"more-content-btn"},{default:pe(()=>i[0]||(i[0]=[ot(" 更多精彩内容 ")])),_:1,__:[0]},8,["to"])])):ue("",!0),e.quickLinks.length>0?(T(),F("div",Iv,[(T(!0),F(ve,null,rt(e.quickLinks,a=>(T(),ge(B(Ct),{key:a.text,to:a.url,class:"footer-link"},{default:pe(()=>[ot(le(a.text),1)]),_:2},1032,["to"]))),128))])):ue("",!0),j("div",Hv,[j("p",Mv,le(e.copyrightText),1),e.companyInfo?(T(),F("p",Dv,le(e.companyInfo),1)):ue("",!0)])])]))}},ho=Tt(Nv,[["__scopeId","data-v-bc69f4a6"]]),Bv={class:"mobile-nav"},jv={class:"nav-content"},Fv={key:1,class:"nav-logo-container"},Vv=["src","alt"],zv={key:2,class:"nav-placeholder"},Uv={key:3,class:"nav-title"},Wv={key:4,class:"nav-title-placeholder"},qv={key:6,class:"nav-placeholder"},Kv={__name:"MobileHeader",props:{title:{type:String,default:""},showBack:{type:Boolean,default:!1},backUrl:{type:String,default:"/"},showLogo:{type:Boolean,default:!0},logoUrl:{type:String,default:"https://img-public.hui1688.cn/shuangma/logo.jpg"},logoAlt:{type:String,default:"双马智能科技"},showHome:{type:Boolean,default:!0},customBack:{type:Function,default:null}},setup(e){const t=e,n=r=>{t.customBack&&(r.preventDefault(),t.customBack())};return(r,o)=>(T(),F("header",Bv,[j("div",jv,[e.showBack?(T(),ge(B(Ct),{key:0,to:e.backUrl,class:"nav-back",onClick:n},{default:pe(()=>o[0]||(o[0]=[ot(" 返回 ")])),_:1,__:[0]},8,["to"])):e.showLogo?(T(),F("div",Fv,[j("img",{src:e.logoUrl,alt:e.logoAlt,class:"nav-logo"},null,8,Vv)])):(T(),F("div",zv)),e.title?(T(),F("h1",Uv,le(e.title),1)):(T(),F("div",Wv)),e.showHome?(T(),ge(B(Ct),{key:5,to:"/",class:"nav-home","aria-label":"返回首页"})):(T(),F("div",qv))])]))}},po=Tt(Kv,[["__scopeId","data-v-94ef3fe4"]]),Gv={class:"mobile-page company-page"},Zv="22-09-01",Jv={__name:"CompanyProfile",setup(e){const t=[{text:"返回首页",url:"/"},{text:"产品展示",url:"/products/"},{text:"生产车间",url:"/workshop/"},{text:"资质证书",url:"/qualification/"}];return(n,r)=>(T(),F("div",Gv,[J(po,{title:"公司简介/Company Profile","show-back":!0,"back-url":"/","show-logo":!1,"show-home":!0}),j("div",{class:"page-content"},[j("section",{class:"page-header"},[j("div",{class:"mobile-container"},[j("div",{class:"header-content"},[r[0]||(r[0]=j("h1",{class:"page-title"},"公司简介/Company Profile",-1)),j("p",{class:"page-date"},le(Zv))])])]),r[1]||(r[1]=so('<section class="main-content" data-v-c26f73ef><div class="mobile-container" data-v-c26f73ef><div class="mobile-content card" data-v-c26f73ef><div class="content-header" data-v-c26f73ef> 公司简介/Company Profile </div><div class="content-body" data-v-c26f73ef><div class="company-image" data-v-c26f73ef><img src="https://img-public.hui1688.cn/shuangma/hero-product.jpg" alt="双马智能科技公司大楼" class="responsive-image" data-v-c26f73ef></div><div class="content-section" data-v-c26f73ef><p data-v-c26f73ef> 无锡市双马智能科技有限公司创立于2006年，历经多年进步和发展，并在2011年创立东莞市双马智能科技有限公司，都专注于电动车电子部件技术的研究和开发、生产与销售，以&quot;电动车仪表&quot;为核心产品，形成了针对电动车行业不同规模、不同应用的多系列产品和针对性解决方案，是国内有影响力的电动车电子部件技术提供商。 </p><p data-v-c26f73ef> 双马智能科技是业内多家企业认定的专用配套企业，总部位于美丽的太湖之乡。同时，双马智能科技以卓越的技术实力，完善的服务体系，被评为电动车行业内多家大型整车企业信得过配套商，成为大多数实力雄厚的企业采购中心指定的电子产品供应商。 </p><p data-v-c26f73ef> 公司拥有多项自主版权的软硬件产品，在&quot;电动车仪表&quot;、&quot;电子解决方案&quot;、&quot;智能网上互动平台&quot;领域卓有建树。&quot;电动车仪表&quot;已经成为行业中知名有影响力的品牌。同时，我们不断在技术工艺和应用进行创新和突破，打破了不同需求、不同定位的电动车电子部件间的信息共享和呈送的技术瓶颈，将电动车电子部件合集群体带上了新的高度。 </p></div><div class="content-section english-section" data-v-c26f73ef><p data-v-c26f73ef> 高速成长的过程中，双马智能科技建立了市场动态TFT系统客户保障机制，形成了以软硬件产品开发、客户产品开发、推广和维护的默契理解运营，理解&quot;以客户为中心&quot;的含义，&quot;服务&quot;贯穿公司运营和管理的每一个细节，赢得广大客户的信任和支持。 </p><p data-v-c26f73ef> With an open and harmonious attitude, Shuangma Intelligent Technology has established in-depth technical cooperation partners with many well-known IT manufacturers and universities at home and abroad, and has continuously deepened its development in the field of industry demand. The growth process of Shuangma intelligent technology is the process of serving customers and continuously succeeding with customers! We work hard to do everything, full of confidence to meet every challenge. </p></div></div></div></div></section>',1))]),J(ho,{"show-back-to-top":!0,"show-more-content":!0,"more-content-url":"/articles/","quick-links":t,"copyright-text":"© 2022-2023 SPINRED 版权所有","company-info":"无锡市双马智能科技有限公司"})]))}},Yv=Tt(Jv,[["__scopeId","data-v-c26f73ef"]]),Qv={key:1},Xv=["src","alt"],e_={class:"card-content"},t_={class:"card-title"},n_={key:0,class:"card-subtitle"},r_={key:1,class:"card-description"},o_={key:0,class:"card-badge"},s_={key:1,class:"card-arrow"},i_={__name:"NavigationCard",props:{title:{type:String,required:!0},subtitle:{type:String,default:""},description:{type:String,default:""},to:{type:[String,Object],default:null},href:{type:String,default:null},target:{type:String,default:"_self"},iconComponent:{type:[String,Object],default:null},iconText:{type:String,default:""},iconImage:{type:String,default:""},iconBgColor:{type:String,default:""},size:{type:String,default:"normal",validator:e=>["small","normal","large"].includes(e)},variant:{type:String,default:"default",validator:e=>["default","primary","secondary"].includes(e)},badge:{type:String,default:""},showArrow:{type:Boolean,default:!0},disabled:{type:Boolean,default:!1},onClick:{type:Function,default:null}},setup(e){const t=e,n=O(()=>t.disabled?"div":t.to?"RouteLink":t.href?"a":"div"),r=O(()=>[`nav-card--${t.size}`,`nav-card--${t.variant}`,{"nav-card--disabled":t.disabled,"nav-card--clickable":!t.disabled&&(t.to||t.href||t.onClick)}]),o=O(()=>{const i={};return t.iconBgColor&&(i.background=t.iconBgColor),i}),s=i=>{if(t.disabled){i.preventDefault();return}t.onClick&&t.onClick(i)};return(i,a)=>(T(),ge(Ks(n.value),{to:e.to,href:e.href,target:e.target,class:je(["nav-card",r.value]),onClick:s},{default:pe(()=>[j("div",{class:"card-icon",style:Yt(o.value)},[e.iconComponent?(T(),ge(Ks(e.iconComponent),{key:0})):e.iconText?(T(),F("span",Qv,le(e.iconText),1)):e.iconImage?(T(),F("img",{key:2,src:e.iconImage,alt:e.title},null,8,Xv)):ue("",!0)],4),j("div",e_,[j("h3",t_,le(e.title),1),e.subtitle?(T(),F("p",n_,le(e.subtitle),1)):ue("",!0),e.description?(T(),F("p",r_,le(e.description),1)):ue("",!0)]),e.badge?(T(),F("div",o_,le(e.badge),1)):ue("",!0),e.showArrow?(T(),F("div",s_,"→")):ue("",!0)]),_:1},8,["to","href","target","class"]))}},wt=Tt(i_,[["__scopeId","data-v-08c6c114"]]),a_={class:"mobile-page home-page"},l_={class:"mobile-hero"},c_={class:"hero-decoration"},u_={class:"decoration-lines"},f_={class:"navigation-section mobile-section"},d_={class:"mobile-container"},h_={class:"mobile-grid grid-2"},p_={__name:"Home",setup(e){const t=[{text:"公司简介",url:"/company/"},{text:"产品展示",url:"/products/"},{text:"联系我们",url:"/contact/"}],n=r=>{const s=(r-1)*16.363636363636363-90,i=Math.abs(Math.cos(s*Math.PI/180))*100+50;return{transform:`rotate(${s}deg)`,width:`${i}px`,left:`${50+(r-6.5)*8}%`,animationDelay:`${r*.1}s`}};return(r,o)=>(T(),F("div",a_,[J(po,{"show-back":!1,"show-logo":!1,"show-home":!1}),j("section",l_,[o[0]||(o[0]=so('<div class="hero-content" data-v-595bb734><div class="hero-logo-container" data-v-595bb734><img src="https://img-public.hui1688.cn/shuangma/logo.jpg" alt="双马智能科技" class="hero-logo" data-v-595bb734></div><h1 class="hero-title" data-v-595bb734>双马智能科技</h1><p class="hero-subtitle" data-v-595bb734>电动车仪表专业生产制造商</p><div class="hero-product" data-v-595bb734><img src="https://img-public.hui1688.cn/shuangma/hero-product.jpg" alt="双马智能科技产品" class="hero-image" data-v-595bb734></div></div>',1)),j("div",c_,[j("div",u_,[(T(),F(ve,null,rt(12,s=>j("div",{key:s,class:"decoration-line",style:Yt(n(s))},null,4)),64))])])]),j("section",f_,[j("div",d_,[j("div",h_,[J(wt,{title:"公司简介",subtitle:"Company profile","icon-text":"🏢",to:"/company/","show-arrow":!0}),J(wt,{title:"产品展示",subtitle:"Product display","icon-text":"🔧",to:"/products/","show-arrow":!0}),J(wt,{title:"生产车间",subtitle:"Production workshop","icon-text":"🏭",to:"/workshop/","show-arrow":!0}),J(wt,{title:"资质证书",subtitle:"Qualification certificates","icon-text":"🎓",to:"/qualification/","show-arrow":!0})])])]),J(ho,{"show-back-to-top":!0,"quick-links":t,"copyright-text":"© 2022-2023 SPINRED 版权所有","company-info":"无锡市双马智能科技有限公司"})]))}},g_=Tt(p_,[["__scopeId","data-v-595bb734"]]),m_={class:"mobile-page product-page"},v_={class:"page-content"},__={class:"product-categories"},y_={class:"mobile-container"},b_={class:"categories-grid"},E_={__name:"ProductDisplay",setup(e){const t=[{text:"返回首页",url:"/"},{text:"公司简介",url:"/company/"},{text:"生产车间",url:"/workshop/"},{text:"资质证书",url:"/qualification/"}];return(n,r)=>(T(),F("div",m_,[J(po,{title:"产品展示","show-back":!0,"back-url":"/","show-logo":!1,"show-home":!0}),j("div",v_,[r[0]||(r[0]=so('<section class="product-hero" data-v-098f21b9><div class="hero-content" data-v-098f21b9><div class="hero-image" data-v-098f21b9><img src="https://img-public.hui1688.cn/shuangma/hero-product.jpg" alt="双马智能科技-电动车仪表专业生产制造商" class="hero-img" data-v-098f21b9></div><div class="hero-text" data-v-098f21b9><h1 class="hero-title" data-v-098f21b9>双马智能科技-电动车仪表专业生产制造商</h1></div></div></section>',1)),j("section",__,[j("div",y_,[j("div",b_,[J(wt,{title:"本月新品",subtitle:"New products this month","icon-text":"🆕","icon-bg-color":"linear-gradient(135deg, #4299e1 0%, #3182ce 100%)",to:"/products/new/",variant:"default",size:"normal","show-arrow":!0}),J(wt,{title:"新品发布",subtitle:"New product release","icon-text":"🚀","icon-bg-color":"linear-gradient(135deg, #48bb78 0%, #38a169 100%)",to:"/products/release/",variant:"default",size:"normal","show-arrow":!0}),J(wt,{title:"电摩系列",subtitle:"Electric motorcycle series","icon-text":"🏍️","icon-bg-color":"linear-gradient(135deg, #9f7aea 0%, #805ad5 100%)",to:"/electric-series/",variant:"default",size:"normal","show-arrow":!0}),J(wt,{title:"国标系列",subtitle:"National Standard series","icon-text":"🏆","icon-bg-color":"linear-gradient(135deg, #ed8936 0%, #dd6b20 100%)",to:"/national-series/",variant:"default",size:"normal","show-arrow":!0}),J(wt,{title:"LCD系列",subtitle:"LCD series","icon-text":"📱","icon-bg-color":"linear-gradient(135deg, #38b2ac 0%, #**********%)",to:"/products/lcd/",variant:"default",size:"normal","show-arrow":!0})])])])]),J(ho,{"show-back-to-top":!0,"quick-links":t,"copyright-text":"© 2022-2023 SPINRED 版权所有","company-info":"无锡市双马智能科技有限公司"})]))}},w_=Tt(E_,[["__scopeId","data-v-098f21b9"]]),S_={class:"page"},C_={class:"tag-wrapper"},T_={class:"tag-num"},x_={__name:"Tag",setup(e){const t=wc("tag"),n=uo();return(r,o)=>(T(),ge(yr,null,{page:pe(()=>[j("main",S_,[j("div",C_,[(T(!0),F(ve,null,rt(B(t).map,({items:s,path:i},a)=>(T(),ge(B(Ct),{key:a,to:i,active:B(n)===i,class:"tag"},{default:pe(()=>[ot(le(a)+" ",1),j("span",T_,le(s.length),1)]),_:2},1032,["to","active"]))),128))]),J(fo,{items:B(t).currentItems??[]},null,8,["items"])])]),_:1}))}},A_={class:"page"},k_={__name:"Timeline",setup(e){const t=Sc("timeline");return(n,r)=>(T(),ge(yr,null,{page:pe(()=>[j("main",A_,[r[0]||(r[0]=j("h1",{class:"timeline-title"},"Timeline",-1)),J(fo,{items:B(t).items,"is-timeline":""},null,8,["items"])])]),_:1}))}},L_=Qt({layouts:{Article:Tv,Category:Lv,Tag:x_,Timeline:k_,Home:g_,CompanyProfile:Yv,ProductDisplay:w_},enhance({app:e,router:t,siteData:n}){e.component("MobileHeader",po),e.component("NavigationCard",wt),e.component("MobileFooter",ho)}}),P_=Object.freeze(Object.defineProperty({__proto__:null,default:L_},Symbol.toStringTag,{value:"Module"})),$r=[gp,Lp,Rp,jp,Qp,og,ag,hg,yg,hv,P_].map(e=>e.default).filter(Boolean),R_=JSON.parse('{"base":"/","lang":"zh-CN","title":"双马智能科技","description":"电动车仪表专业生产制造商","head":[["meta",{"name":"viewport","content":"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"}],["meta",{"name":"apple-mobile-web-app-capable","content":"yes"}],["meta",{"name":"apple-mobile-web-app-status-bar-style","content":"black-translucent"}],["link",{"rel":"icon","href":"/favicon.ico"}]],"locales":{}}');var Vn=Pe(R_),$_=Kd,O_=()=>{const e=_h({history:$_(Al("/")),routes:[{name:"vuepress-route",path:"/:catchAll(.*)",components:{}}],scrollBehavior:(t,n,r)=>r||(t.hash?{el:t.hash}:{top:0})});return e.beforeResolve(async(t,n)=>{if(t.path!==n.path||n===Pt){const r=dn(t.fullPath);if(r.path!==t.fullPath)return r.path;const o=await r.loader();t.meta={...r.meta,_pageChunk:o}}else t.path===n.path&&(t.meta=n.meta)}),e},I_=e=>{e.component("ClientOnly",Cs),e.component("Content",Ts),e.component("RouteLink",Ct)},H_=(e,t,n)=>{const r=O(()=>t.currentRoute.value.path),o=$a((w,A)=>({get(){return w(),t.currentRoute.value.meta._pageChunk},set(k){t.currentRoute.value.meta._pageChunk=k,A()}})),s=O(()=>nn.resolveLayouts(n)),i=O(()=>nn.resolveRouteLocale(Vn.value.locales,r.value)),a=O(()=>nn.resolveSiteLocaleData(Vn.value,i.value)),l=O(()=>o.value.comp),c=O(()=>o.value.data),u=O(()=>c.value.frontmatter),f=O(()=>nn.resolvePageHeadTitle(c.value,a.value)),p=O(()=>nn.resolvePageHead(f.value,u.value,a.value)),g=O(()=>nn.resolvePageLang(c.value,a.value)),_=O(()=>nn.resolvePageLayout(c.value,s.value)),b={layouts:s,pageData:c,pageComponent:l,pageFrontmatter:u,pageHead:p,pageHeadTitle:f,pageLang:g,pageLayout:_,redirects:Qo,routeLocale:i,routePath:r,routes:Tn,siteData:Vn,siteLocaleData:a};return e.provide(Es,b),Object.defineProperties(e.config.globalProperties,{$frontmatter:{get:()=>u.value},$head:{get:()=>p.value},$headTitle:{get:()=>f.value},$lang:{get:()=>g.value},$page:{get:()=>c.value},$routeLocale:{get:()=>i.value},$site:{get:()=>Vn.value},$siteLocale:{get:()=>a.value},$withBase:{get:()=>xs}}),b},M_=([e,t,n=""])=>{const r=Object.entries(t).map(([a,l])=>pt(l)?`[${a}=${JSON.stringify(l)}]`:l?`[${a}]`:"").join(""),o=`head > ${e}${r}`;return Array.from(document.querySelectorAll(o)).find(a=>a.innerText===n)??null},D_=([e,t,n])=>{if(!pt(e))return null;const r=document.createElement(e);return _s(t)&&Object.entries(t).forEach(([o,s])=>{pt(s)?r.setAttribute(o,s):s&&r.setAttribute(o,"")}),pt(n)&&r.appendChild(document.createTextNode(n)),r},N_=()=>{const e=Eh(),t=ws();let n=[];const r=()=>{e.value.forEach(i=>{const a=M_(i);a&&n.push(a)})},o=()=>{const i=[];return e.value.forEach(a=>{const l=D_(a);l&&i.push(l)}),i},s=()=>{document.documentElement.lang=t.value;const i=o();n.forEach((a,l)=>{const c=i.findIndex(u=>a.isEqualNode(u));c===-1?(a.remove(),delete n[l]):i.splice(c,1)}),i.forEach(a=>document.head.appendChild(a)),n=[...n.filter(a=>!!a),...i]};Kt(Th,s),st(()=>{r(),ut(e,s,{immediate:!1})})},B_=Xf,j_=async()=>{var n;const e=B_({name:"Vuepress",setup(){var s;N_();for(const i of $r)(s=i.setup)==null||s.call(i);const r=$r.flatMap(({rootComponents:i=[]})=>i.map(a=>te(a))),o=wh();return()=>[te(o.value),r]}}),t=O_();I_(e),H_(e,t,$r);for(const r of $r)await((n=r.enhance)==null?void 0:n.call(r,{app:e,router:t,siteData:Vn}));return e.use(t),{app:e,router:t}};j_().then(({app:e,router:t})=>{t.isReady().then(()=>{e.mount("#app")})});export{Tt as _,so as a,j as b,F as c,j_ as createVueApp,T as o};
