import{_ as a,c as t,a as n,o as i}from"./app-Dc7sG0MX.js";const r={};function h(s,e){return i(),t("div",null,e[0]||(e[0]=[n('<h1 id="article-11" tabindex="-1"><a class="header-anchor" href="#article-11"><span>Article 11</span></a></h1><h2 id="heading-2" tabindex="-1"><a class="header-anchor" href="#heading-2"><span>Heading 2</span></a></h2><p>Here is the content.</p><h3 id="heading-3" tabindex="-1"><a class="header-anchor" href="#heading-3"><span>Heading 3</span></a></h3><p>Here is the content.</p>',5)]))}const d=a(r,[["render",h]]),l=JSON.parse('{"path":"/posts/article11.html","title":"Article 11","lang":"zh-CN","frontmatter":{"date":"2022-01-11T00:00:00.000Z","category":["Category A","Category B"],"tag":["tag C","tag D"]},"headers":[{"level":2,"title":"Heading 2","slug":"heading-2","link":"#heading-2","children":[{"level":3,"title":"Heading 3","slug":"heading-3","link":"#heading-3","children":[]}]}],"git":{},"filePathRelative":"posts/article11.md","excerpt":"\\n<h2>Heading 2</h2>\\n<p>Here is the content.</p>\\n<h3>Heading 3</h3>\\n<p>Here is the content.</p>\\n"}');export{d as comp,l as data};
