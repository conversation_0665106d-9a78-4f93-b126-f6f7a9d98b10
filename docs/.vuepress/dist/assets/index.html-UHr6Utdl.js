import{_ as t,c as e,o as a}from"./app-Dc7sG0MX.js";const r={};function c(o,n){return a(),e("div")}const s=t(r,[["render",c]]),g=JSON.parse('{"path":"/tag/tag-b/","title":"Tag tag B","lang":"zh-CN","frontmatter":{"title":"Tag tag B","sidebar":false,"blog":{"type":"category","name":"tag B","key":"tag"},"layout":"Tag"},"headers":[],"git":{},"filePathRelative":null,"excerpt":""}');export{s as comp,g as data};
