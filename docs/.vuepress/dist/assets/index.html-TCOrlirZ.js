import{_ as e,c as t,o as a}from"./app-Dc7sG0MX.js";const r={};function o(c,s){return a(),t("div")}const l=e(r,[["render",o]]),i=JSON.parse('{"path":"/category/","title":"Categories","lang":"zh-CN","frontmatter":{"title":"Categories","sidebar":false,"blog":{"type":"category","key":"category"},"layout":"Category"},"headers":[],"git":{},"filePathRelative":null,"excerpt":""}');export{l as comp,i as data};
