import{_ as t,c as a,o as e}from"./app-Dc7sG0MX.js";const r={};function c(o,n){return e(),a("div")}const s=t(r,[["render",c]]),g=JSON.parse('{"path":"/tag/tag-a/","title":"Tag tag A","lang":"zh-CN","frontmatter":{"title":"Tag tag A","sidebar":false,"blog":{"type":"category","name":"tag A","key":"tag"},"layout":"Tag"},"headers":[],"git":{},"filePathRelative":null,"excerpt":""}');export{s as comp,g as data};
