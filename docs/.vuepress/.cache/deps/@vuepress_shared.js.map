{"version": 3, "sources": ["../../../../node_modules/.pnpm/@vuepress+shared@2.0.0-rc.20/node_modules/@vuepress/shared/dist/index.js"], "sourcesContent": ["// src/utils/links/isLinkWithProtocol.ts\nvar isLinkWithProtocol = (link) => /^[a-z][a-z0-9+.-]*:/.test(link) || link.startsWith(\"//\");\n\n// src/utils/links/isLinkExternal.ts\nvar markdownLinkRegexp = /.md((\\?|#).*)?$/;\nvar isLinkExternal = (link, base = \"/\") => isLinkWithProtocol(link) || // absolute link that does not start with `base` and does not end with `.md`\nlink.startsWith(\"/\") && !link.startsWith(base) && !markdownLinkRegexp.test(link);\n\n// src/utils/links/isLinkHttp.ts\nvar isLinkHttp = (link) => /^(https?:)?\\/\\//.test(link);\n\n// src/utils/routes/inferRoutePath.ts\nvar inferRoutePath = (rawPath) => {\n  if (!rawPath || rawPath.endsWith(\"/\")) return rawPath;\n  let routePath = rawPath.replace(/(^|\\/)README.md$/i, \"$1index.html\");\n  if (routePath.endsWith(\".md\")) {\n    routePath = `${routePath.substring(0, routePath.length - 3)}.html`;\n  } else if (!routePath.endsWith(\".html\")) {\n    routePath = `${routePath}.html`;\n  }\n  if (routePath.endsWith(\"/index.html\")) {\n    routePath = routePath.substring(0, routePath.length - 10);\n  }\n  return routePath;\n};\n\n// src/utils/routes/normalizeRoutePath.ts\nvar FAKE_HOST = \"http://.\";\nvar normalizeRoutePath = (pathname, current) => {\n  if (!pathname.startsWith(\"/\") && current) {\n    const loc = current.slice(0, current.lastIndexOf(\"/\"));\n    return inferRoutePath(new URL(`${loc}/${pathname}`, FAKE_HOST).pathname);\n  }\n  return inferRoutePath(pathname);\n};\n\n// src/utils/routes/resolveLocalePath.ts\nvar resolveLocalePath = (locales, routePath) => {\n  const localePaths = Object.keys(locales).sort((a, b) => {\n    const levelDelta = b.split(\"/\").length - a.split(\"/\").length;\n    if (levelDelta !== 0) {\n      return levelDelta;\n    }\n    return b.length - a.length;\n  });\n  for (const localePath of localePaths) {\n    if (routePath.startsWith(localePath)) {\n      return localePath;\n    }\n  }\n  return \"/\";\n};\n\n// src/utils/routes/resolveRoutePathFromUrl.ts\nvar resolveRoutePathFromUrl = (url, base = \"/\") => {\n  const pathname = url.replace(/^(?:https?:)?\\/\\/[^/]*/, \"\");\n  return pathname.startsWith(base) ? `/${pathname.slice(base.length)}` : pathname;\n};\n\n// src/utils/routes/splitPath.ts\nvar SPLIT_CHAR_REGEXP = /(#|\\?)/;\nvar splitPath = (path) => {\n  const [pathname, ...hashAndQueries] = path.split(SPLIT_CHAR_REGEXP);\n  return {\n    pathname,\n    hashAndQueries: hashAndQueries.join(\"\")\n  };\n};\n\n// src/utils/resolveHeadIdentifier.ts\nvar TAGS_ALLOWED = [\"link\", \"meta\", \"script\", \"style\", \"noscript\", \"template\"];\nvar TAGS_UNIQUE = [\"title\", \"base\"];\nvar resolveHeadIdentifier = ([tag, attrs, content]) => {\n  if (TAGS_UNIQUE.includes(tag)) {\n    return tag;\n  }\n  if (!TAGS_ALLOWED.includes(tag)) {\n    return null;\n  }\n  if (tag === \"meta\" && attrs.name) {\n    return `${tag}.${attrs.name}`;\n  }\n  if (tag === \"template\" && attrs.id) {\n    return `${tag}.${attrs.id}`;\n  }\n  return JSON.stringify([\n    tag,\n    Object.entries(attrs).map(([key, value]) => {\n      if (typeof value === \"boolean\") {\n        return value ? [key, \"\"] : null;\n      }\n      return [key, value];\n    }).filter((item) => item != null).sort(([keyA], [keyB]) => keyA.localeCompare(keyB)),\n    content\n  ]);\n};\n\n// src/utils/dedupeHead.ts\nvar dedupeHead = (head) => {\n  const identifierSet = /* @__PURE__ */ new Set();\n  const result = [];\n  head.forEach((item) => {\n    const identifier = resolveHeadIdentifier(item);\n    if (identifier && !identifierSet.has(identifier)) {\n      identifierSet.add(identifier);\n      result.push(item);\n    }\n  });\n  return result;\n};\n\n// src/utils/ensureLeadingSlash.ts\nvar ensureLeadingSlash = (str) => str.startsWith(\"/\") ? str : `/${str}`;\n\n// src/utils/ensureEndingSlash.ts\nvar ensureEndingSlash = (str) => str.endsWith(\"/\") || str.endsWith(\".html\") ? str : `${str}/`;\n\n// src/utils/formatDateString.ts\nvar formatDateString = (str, defaultDateString = \"\") => {\n  const dateMatch = str.match(/\\b(\\d{4})-(\\d{1,2})-(\\d{1,2})\\b/);\n  if (dateMatch === null) {\n    return defaultDateString;\n  }\n  const [, yearStr, monthStr, dayStr] = dateMatch;\n  return [yearStr, monthStr.padStart(2, \"0\"), dayStr.padStart(2, \"0\")].join(\"-\");\n};\n\n// src/utils/omit.ts\nvar omit = (obj, ...keys) => {\n  const result = { ...obj };\n  for (const key of keys) {\n    delete result[key];\n  }\n  return result;\n};\n\n// src/utils/removeEndingSlash.ts\nvar removeEndingSlash = (str) => str.endsWith(\"/\") ? str.slice(0, -1) : str;\n\n// src/utils/removeLeadingSlash.ts\nvar removeLeadingSlash = (str) => str.startsWith(\"/\") ? str.slice(1) : str;\n\n// src/utils/typeGuards.ts\nvar isFunction = (val) => typeof val === \"function\";\nvar isPlainObject = (val) => Object.prototype.toString.call(val) === \"[object Object]\";\nvar isString = (val) => typeof val === \"string\";\nexport {\n  dedupeHead,\n  ensureEndingSlash,\n  ensureLeadingSlash,\n  formatDateString,\n  inferRoutePath,\n  isFunction,\n  isLinkExternal,\n  isLinkHttp,\n  isLinkWithProtocol,\n  isPlainObject,\n  isString,\n  normalizeRoutePath,\n  omit,\n  removeEndingSlash,\n  removeLeadingSlash,\n  resolveHeadIdentifier,\n  resolveLocalePath,\n  resolveRoutePathFromUrl,\n  splitPath\n};\n"], "mappings": ";AACA,IAAI,qBAAqB,CAAC,SAAS,sBAAsB,KAAK,IAAI,KAAK,KAAK,WAAW,IAAI;AAG3F,IAAI,qBAAqB;AACzB,IAAI,iBAAiB,CAAC,MAAM,OAAO,QAAQ,mBAAmB,IAAI;AAClE,KAAK,WAAW,GAAG,KAAK,CAAC,KAAK,WAAW,IAAI,KAAK,CAAC,mBAAmB,KAAK,IAAI;AAG/E,IAAI,aAAa,CAAC,SAAS,kBAAkB,KAAK,IAAI;AAGtD,IAAI,iBAAiB,CAAC,YAAY;AAChC,MAAI,CAAC,WAAW,QAAQ,SAAS,GAAG,EAAG,QAAO;AAC9C,MAAI,YAAY,QAAQ,QAAQ,qBAAqB,cAAc;AACnE,MAAI,UAAU,SAAS,KAAK,GAAG;AAC7B,gBAAY,GAAG,UAAU,UAAU,GAAG,UAAU,SAAS,CAAC,CAAC;AAAA,EAC7D,WAAW,CAAC,UAAU,SAAS,OAAO,GAAG;AACvC,gBAAY,GAAG,SAAS;AAAA,EAC1B;AACA,MAAI,UAAU,SAAS,aAAa,GAAG;AACrC,gBAAY,UAAU,UAAU,GAAG,UAAU,SAAS,EAAE;AAAA,EAC1D;AACA,SAAO;AACT;AAGA,IAAI,YAAY;AAChB,IAAI,qBAAqB,CAAC,UAAU,YAAY;AAC9C,MAAI,CAAC,SAAS,WAAW,GAAG,KAAK,SAAS;AACxC,UAAM,MAAM,QAAQ,MAAM,GAAG,QAAQ,YAAY,GAAG,CAAC;AACrD,WAAO,eAAe,IAAI,IAAI,GAAG,GAAG,IAAI,QAAQ,IAAI,SAAS,EAAE,QAAQ;AAAA,EACzE;AACA,SAAO,eAAe,QAAQ;AAChC;AAGA,IAAI,oBAAoB,CAAC,SAAS,cAAc;AAC9C,QAAM,cAAc,OAAO,KAAK,OAAO,EAAE,KAAK,CAAC,GAAG,MAAM;AACtD,UAAM,aAAa,EAAE,MAAM,GAAG,EAAE,SAAS,EAAE,MAAM,GAAG,EAAE;AACtD,QAAI,eAAe,GAAG;AACpB,aAAO;AAAA,IACT;AACA,WAAO,EAAE,SAAS,EAAE;AAAA,EACtB,CAAC;AACD,aAAW,cAAc,aAAa;AACpC,QAAI,UAAU,WAAW,UAAU,GAAG;AACpC,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAGA,IAAI,0BAA0B,CAAC,KAAK,OAAO,QAAQ;AACjD,QAAM,WAAW,IAAI,QAAQ,0BAA0B,EAAE;AACzD,SAAO,SAAS,WAAW,IAAI,IAAI,IAAI,SAAS,MAAM,KAAK,MAAM,CAAC,KAAK;AACzE;AAGA,IAAI,oBAAoB;AACxB,IAAI,YAAY,CAAC,SAAS;AACxB,QAAM,CAAC,UAAU,GAAG,cAAc,IAAI,KAAK,MAAM,iBAAiB;AAClE,SAAO;AAAA,IACL;AAAA,IACA,gBAAgB,eAAe,KAAK,EAAE;AAAA,EACxC;AACF;AAGA,IAAI,eAAe,CAAC,QAAQ,QAAQ,UAAU,SAAS,YAAY,UAAU;AAC7E,IAAI,cAAc,CAAC,SAAS,MAAM;AAClC,IAAI,wBAAwB,CAAC,CAAC,KAAK,OAAO,OAAO,MAAM;AACrD,MAAI,YAAY,SAAS,GAAG,GAAG;AAC7B,WAAO;AAAA,EACT;AACA,MAAI,CAAC,aAAa,SAAS,GAAG,GAAG;AAC/B,WAAO;AAAA,EACT;AACA,MAAI,QAAQ,UAAU,MAAM,MAAM;AAChC,WAAO,GAAG,GAAG,IAAI,MAAM,IAAI;AAAA,EAC7B;AACA,MAAI,QAAQ,cAAc,MAAM,IAAI;AAClC,WAAO,GAAG,GAAG,IAAI,MAAM,EAAE;AAAA,EAC3B;AACA,SAAO,KAAK,UAAU;AAAA,IACpB;AAAA,IACA,OAAO,QAAQ,KAAK,EAAE,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM;AAC1C,UAAI,OAAO,UAAU,WAAW;AAC9B,eAAO,QAAQ,CAAC,KAAK,EAAE,IAAI;AAAA,MAC7B;AACA,aAAO,CAAC,KAAK,KAAK;AAAA,IACpB,CAAC,EAAE,OAAO,CAAC,SAAS,QAAQ,IAAI,EAAE,KAAK,CAAC,CAAC,IAAI,GAAG,CAAC,IAAI,MAAM,KAAK,cAAc,IAAI,CAAC;AAAA,IACnF;AAAA,EACF,CAAC;AACH;AAGA,IAAI,aAAa,CAAC,SAAS;AACzB,QAAM,gBAAgC,oBAAI,IAAI;AAC9C,QAAM,SAAS,CAAC;AAChB,OAAK,QAAQ,CAAC,SAAS;AACrB,UAAM,aAAa,sBAAsB,IAAI;AAC7C,QAAI,cAAc,CAAC,cAAc,IAAI,UAAU,GAAG;AAChD,oBAAc,IAAI,UAAU;AAC5B,aAAO,KAAK,IAAI;AAAA,IAClB;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAGA,IAAI,qBAAqB,CAAC,QAAQ,IAAI,WAAW,GAAG,IAAI,MAAM,IAAI,GAAG;AAGrE,IAAI,oBAAoB,CAAC,QAAQ,IAAI,SAAS,GAAG,KAAK,IAAI,SAAS,OAAO,IAAI,MAAM,GAAG,GAAG;AAG1F,IAAI,mBAAmB,CAAC,KAAK,oBAAoB,OAAO;AACtD,QAAM,YAAY,IAAI,MAAM,iCAAiC;AAC7D,MAAI,cAAc,MAAM;AACtB,WAAO;AAAA,EACT;AACA,QAAM,CAAC,EAAE,SAAS,UAAU,MAAM,IAAI;AACtC,SAAO,CAAC,SAAS,SAAS,SAAS,GAAG,GAAG,GAAG,OAAO,SAAS,GAAG,GAAG,CAAC,EAAE,KAAK,GAAG;AAC/E;AAGA,IAAI,OAAO,CAAC,QAAQ,SAAS;AAC3B,QAAM,SAAS,EAAE,GAAG,IAAI;AACxB,aAAW,OAAO,MAAM;AACtB,WAAO,OAAO,GAAG;AAAA,EACnB;AACA,SAAO;AACT;AAGA,IAAI,oBAAoB,CAAC,QAAQ,IAAI,SAAS,GAAG,IAAI,IAAI,MAAM,GAAG,EAAE,IAAI;AAGxE,IAAI,qBAAqB,CAAC,QAAQ,IAAI,WAAW,GAAG,IAAI,IAAI,MAAM,CAAC,IAAI;AAGvE,IAAI,aAAa,CAAC,QAAQ,OAAO,QAAQ;AACzC,IAAI,gBAAgB,CAAC,QAAQ,OAAO,UAAU,SAAS,KAAK,GAAG,MAAM;AACrE,IAAI,WAAW,CAAC,QAAQ,OAAO,QAAQ;", "names": []}