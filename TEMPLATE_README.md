# 双马智能科技移动端模板系统

基于VuePress 2.x构建的移动端优先响应式网站模板系统，专为双马智能科技官网设计。

## 🚀 快速开始

### 安装依赖

```bash
pnpm install
```

### 开发模式

```bash
pnpm run docs:dev
```

### 构建生产版本

```bash
pnpm run docs:build
```

## 📱 设计特性

### 移动端优先
- 基于设计稿精确还原的移动端界面
- 触摸友好的交互设计
- 优化的移动端性能

### 响应式设计
- 适配多种屏幕尺寸
- 流畅的断点过渡
- 灵活的网格布局系统

### 现代化技术栈
- VuePress 2.x + Vue 3
- SCSS样式预处理
- ES6+ JavaScript
- 组件化架构

## 🎨 模板系统

### 页面布局

1. **Home** - 首页布局
   - 品牌展示区域
   - 产品导航卡片
   - 装饰性动画效果

2. **CompanyProfile** - 公司简介布局
   - 图文混排内容
   - 中英文双语支持
   - 响应式图片展示

3. **ProductDisplay** - 产品展示布局
   - 产品分类导航
   - 渐变色彩设计
   - 交互动画效果

### 核心组件

- **MobileHeader** - 移动端导航头部
- **NavigationCard** - 导航卡片组件
- **MobileFooter** - 移动端页脚

## 🎯 使用方法

### 创建新页面

1. 在相应目录创建Markdown文件
2. 设置frontmatter指定布局
3. 编写页面内容

```markdown
---
layout: CompanyProfile
title: 页面标题
description: 页面描述
---

# 页面内容
```

### 自定义组件

```vue
<NavigationCard
  title="卡片标题"
  subtitle="副标题"
  icon-text="🏢"
  to="/company/"
/>
```

## 📁 项目结构

```
docs/
├── .vuepress/
│   ├── components/          # 可复用组件
│   ├── layouts/            # 页面布局
│   ├── styles/             # 样式文件
│   ├── config.js           # VuePress配置
│   └── client.js           # 客户端配置
├── company/                # 公司简介页面
├── products/               # 产品展示页面
├── workshop/               # 生产车间页面
├── qualification/          # 资质证书页面
└── README.md               # 首页
```

## 🎨 设计系统

### 色彩规范
- 主色：#e53e3e (双马红)
- 辅色：#000000 (黑色)
- 背景：#ffffff (白色)
- 灰色：#f5f5f5 (浅灰)

### 字体规范
- 基础字号：16px
- 行高：1.6
- 字体：系统字体栈

### 间距规范
- 基础间距：4px的倍数
- 常用间距：8px, 16px, 24px, 32px

## 📱 移动端优化

### 性能优化
- 图片懒加载
- 代码分割
- 资源压缩
- 缓存策略

### 用户体验
- 触摸目标大小 ≥ 44px
- 快速响应的交互反馈
- 流畅的动画过渡
- 直观的导航结构

## 🔧 技术规范

### 组件开发
- 使用Vue 3 Composition API
- 遵循单一职责原则
- 提供完整的Props类型定义
- 支持插槽和事件

### 样式规范
- 使用SCSS预处理器
- CSS变量定义设计令牌
- BEM命名规范
- 移动端优先的媒体查询

### 代码规范
- ESLint代码检查
- Prettier代码格式化
- 组件和函数注释
- 类型安全

## 📖 文档

- [模板使用指南](./docs/template-guide.md)
- [组件API文档](./docs/template-guide.md#核心组件)
- [样式系统文档](./docs/template-guide.md#样式系统)

## 🚀 部署

### 构建
```bash
pnpm run docs:build
```

### 部署到服务器
1. 将dist目录上传到服务器
2. 配置Web服务器支持SPA路由
3. 设置适当的缓存策略

### 推荐部署平台
- Vercel
- Netlify
- GitHub Pages
- 阿里云OSS

## 🤝 贡献

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 📄 许可证

MIT License

## 📞 技术支持

如有问题或建议，请联系开发团队。

---

**双马智能科技** - 电动车仪表专业生产制造商
